from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
import os
from pathlib import Path

# Database configuration
BASE_DIR = Path(__file__).resolve().parent.parent
DATABASE_URL = f"sqlite:///{os.path.join(BASE_DIR, 'green_nest_trade_co.db')}"

# Create SQLAlchemy engine
engine = create_engine(
    DATABASE_URL, connect_args={"check_same_thread": False}
)

# Create a scoped session factory
SessionLocal = scoped_session(
    sessionmaker(autocommit=False, autoflush=False, bind=engine)
)

# Base class for all models
Base = declarative_base()

def get_db():
    """
    Dependency function to get DB session.
    Use this in your FastAPI dependencies or other contexts.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_db():
    """
    Initialize the database by creating all tables.
    Call this function when the application starts.
    """
    # Import all models here to ensure they are registered with SQLAlchemy
    from models.invoice import Invoice, InvoiceLine
    from models.customer import Customer
    from models.product import Product
    from models.user import User
    
    Base.metadata.create_all(bind=engine)
