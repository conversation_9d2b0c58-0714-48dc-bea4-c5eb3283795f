---
description: 
globs: .py
alwaysApply: false
---
These guidelines outline best practices for developing a robust, scalable, and maintainable accounting application in Python, focusing on PyQt6 for the GUI, pandas and openpyxl for data handling, SQLAlchemy for database operations, qdarkstyle for theming, bcrypt for secure password hashing, and python-dotenv for environment variable management.

Key Principles:

Write concise, modular, and maintainable Python code with clear documentation.

Use functional programming where possible; avoid unnecessary class complexity.

Prioritize data accuracy, security, and user experience for financial applications.

Use descriptive variable names with auxiliary verbs (e.g., is_posted, has_balance).

Organize files in lowercase with underscores (e.g., gui/main_window.py, utils/data_validators.py).

Prefer named exports for utility functions, GUI components, and database operations.

Follow the Receive an Object, Return an Object (RORO) pattern for functions.

Ensure compliance with accounting standards (e.g., double-entry bookkeeping, audit trails).




Python and Library-Specific Guidelines:

PyQt6 and PyQt6-Tools
Use PyQt6 for creating a responsive and cross-platform GUI for the accounting app.
Leverage QMainWindow for the main application window and QWidget for modular components (e.g., ledger view, transaction form).
Organize UI components in separate files (e.g., gui/ledger_widget.py, gui/reports_widget.py).

Use PyQt6-Tools for designing UI with Qt Designer and converting .ui files to Python code using pyuic6.

Implement signals and slots for event-driven interactions (e.g., button clicks, form submissions).

Avoid hardcoding UI styles; use qdarkstyle for consistent dark theme application.

Optimize UI performance by minimizing heavy computations in the main thread; use QThread for background tasks (e.g., generating financial reports).



Pandas and Openpyxl :

Use pandas for in-memory data manipulation, such as ledger calculations, balance computations, and financial reporting.

Prefer pandas.DataFrame for handling tabular data like transactions, accounts, and journals.
Use openpyxl for reading and writing Excel files for importing/exporting accounting data (e.g., trial balances, financial statements).

Validate data formats before processing (e.g., ensure dates are in YYYY-MM-DD, amounts are numeric).

Implement data cleaning and transformation pipelines in pandas to handle malformed or incomplete financial data.

Optimize large datasets by using pandas chunking or openpyxl streaming for Excel files.



SQLAlchemy:

Use SQLAlchemy 2.0 for database operations to manage accounting data (e.g., transactions, accounts, journals).

Prefer async operations with SQLAlchemy’s asyncio extension for non-blocking database queries.

Define clear database models using declarative base (e.g., Account, Transaction, JournalEntry).

Enforce double-entry bookkeeping by ensuring every transaction has balanced debits and credits.

Implement audit trails by logging all database changes (e.g., creation, update, deletion) with timestamps and user IDs.

Use database constraints (e.g., foreign keys, unique constraints) to maintain data integrity.

Store sensitive data (e.g., connection strings) in environment variables using python-dotenv.



QDarkStyle:

Apply qdarkstyle for a professional, dark-themed UI to enhance user experience.

Load the stylesheet globally in the main application to ensure consistency across all widgets.

Customize styles sparingly to avoid overriding qdarkstyle defaults unnecessarily.

Test UI rendering on different platforms to ensure compatibility and readability.

Bcrypt:
Use bcrypt for secure password hashing to protect user credentials.

Implement password hashing and verification in a dedicated utility module (e.g., utils/auth_utils.py).

Enforce strong password policies (e.g., minimum length, complexity requirements).

Store hashed passwords in the database, never plain text.

Use a high work factor for bcrypt to balance security and performance.



Python-Dotenv:
Use python-dotenv to manage environment variables for configuration (e.g., database URLs, API keys).
Store sensitive information in a .env file and load it at application startup.
Ensure .env is included in .gitignore to prevent accidental exposure.
Use descriptive variable names in .env (e.g., DB_HOST, DB_PASSWORD).



Accounting App-Specific Guidelines:

Data Integrity: Enforce double-entry bookkeeping principles (every transaction affects at least two accounts with equal debits and credits).

Audit Trails: Log all user actions (e.g., transaction creation, edits, deletions) with timestamps, user IDs, and change details.

Financial Reporting: Implement standard reports (e.g., balance sheet, income statement, trial balance) using pandas for calculations and PyQt6 for visualization.

Data Validation: Validate all inputs (e.g., transaction amounts, dates, account codes) before processing to prevent errors.

Reconciliation: Provide tools for reconciling accounts (e.g., bank reconciliations) with visual feedback in the GUI.

Multi-User Support: Implement role-based access control (e.g., admin, accountant, viewer) using bcrypt for authentication.

Export/Import: Support data export/import in common formats (e.g., CSV, Excel) using openpyxl.

Backup and Recovery: Implement automated database backups and a recovery mechanism to prevent data loss.

Localization: Support multiple currencies and date formats for international users.

Error Handling: Provide user-friendly error messages for invalid inputs or failed operations (e.g., unbalanced transactions).

Error Handling and Validation:

Handle errors at the start of functions using guard clauses and early returns.

Validate financial data before processing (e.g., ensure transaction amounts are non-negative, accounts exist).

Use custom exceptions (e.g., InvalidTransactionError, UnbalancedEntryError) for accounting-specific errors.

Log errors to a file or database with sufficient context (e.g., user ID, timestamp, operation).

Display user-friendly error messages in the GUI using QMessageBox.

Avoid nested conditionals; prefer if-return patterns for clarity.



Performance Optimization:

Use async database operations with SQLAlchemy for large datasets to avoid blocking the GUI.

Cache frequently accessed data (e.g., account lists, recent transactions) in memory using pandas or a lightweight cache.

Optimize Excel operations with openpyxl by using read-only or write-only modes for large files.

Use QThread or QProcess for computationally intensive tasks (e.g., generating large financial reports).

Minimize GUI redraws by updating only necessary widgets with PyQt6.

Key Conventions:

Use PyQt6’s signal-slot mechanism for clean event handling.

Prioritize data accuracy with strict validation and database constraints.

Use SQLAlchemy for transactional integrity and rollback on errors.

Optimize GUI responsiveness by offloading heavy computations to background threads.

Ensure secure storage of sensitive data with bcrypt and python-dotenv.

Follow accounting principles (e.g., GAAP, IFRS) for data consistency.

Test all components (GUI, data processing, database) for edge cases and errors.