"""
Add unique constraint on (invoice_number, supplier_id) in invoices table
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240715_add_unique_invoice_number_per_supplier'
down_revision = 'c8c8513c0ada'
branch_labels = None
depends_on = None

def upgrade():
    with op.batch_alter_table('invoices', recreate='always') as batch_op:
        batch_op.create_unique_constraint(
            'uq_invoice_number_supplier',
            ['invoice_number', 'supplier_id']
        )

def downgrade():
    with op.batch_alter_table('invoices', recreate='always') as batch_op:
        batch_op.drop_constraint('uq_invoice_number_supplier', type_='unique') 