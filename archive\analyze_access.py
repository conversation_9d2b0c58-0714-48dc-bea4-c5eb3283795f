#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to analyze the structure of an Access database and generate a report.
"""
import json
from pathlib import Path
from database.access_db import analyze_access_database

def save_structure_to_file(structure: dict, output_file: str):
    """Save the database structure to a JSON file."""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(structure, f, indent=2, ensure_ascii=False, default=str)
    print(f"\nDatabase structure saved to {output_file}")

def main():
    # Path to your Access database
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    
    # Database credentials
    username = "majid"
    password = "majid"
    
    # Output file for the structure
    output_file = "database_structure.json"
    
    print(f"Analyzing database: {db_path}")
    
    # Check if database file exists
    if not Path(db_path).exists():
        print(f"Error: Database file not found at {db_path}")
        return
    
    # Analyze the database
    structure = analyze_access_database(db_path, username, password)
    
    # Save the structure to a file
    save_structure_to_file(structure, output_file)
    
    # Print summary
    print("\n=== Analysis Complete ===")
    print(f"Tables found: {len(structure.get('tables', {}))}")
    print(f"Relationships found: {len(structure.get('relationships', []))}")
    
    # Print table names
    if 'tables' in structure:
        print("\nTables:")
        for i, table_name in enumerate(structure['tables'].keys(), 1):
            print(f"  {i}. {table_name}")
    
    print(f"\nDetailed structure has been saved to: {output_file}")

if __name__ == "__main__":
    main()
