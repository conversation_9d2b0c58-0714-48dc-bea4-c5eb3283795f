"""
User Activity Logging Model
Tracks user actions and activities in the application.
"""
from sqlalchemy import Column, String, Integer, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from .base import BaseModel

class UserActivity(BaseModel):
    """Model for tracking user activities and actions."""
    __tablename__ = "user_activities"
    
    # User reference
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    user = relationship("User")
    
    # Activity details
    action = Column(String(100), nullable=False)  # e.g., 'login', 'create_invoice', 'update_customer'
    module = Column(String(50), nullable=True)   # e.g., 'dashboard', 'customers', 'invoices'
    description = Column(Text, nullable=True)    # Detailed description of the action
    
    # Session information
    session_id = Column(String(100), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(Text, nullable=True)
    
    # Timing
    duration_ms = Column(Integer, nullable=True)  # Duration in milliseconds for timed actions
    
    # Additional metadata
    entity_type = Column(String(50), nullable=True)  # Type of entity affected (e.g., 'customer', 'invoice')
    entity_id = Column(Integer, nullable=True)       # ID of the affected entity
    old_values = Column(Text, nullable=True)         # JSON string of old values (for updates)
    new_values = Column(Text, nullable=True)         # JSON string of new values (for updates)
    
    def __repr__(self):
        return f"<UserActivity {self.action} by user {self.user_id} at {self.created_at}>"
    
    @classmethod
    def log_activity(cls, session, user_id: int, action: str, module: str = None, 
                    description: str = None, entity_type: str = None, entity_id: int = None,
                    session_id: str = None, ip_address: str = None, user_agent: str = None,
                    old_values: str = None, new_values: str = None, duration_ms: int = None):
        """
        Log a user activity.
        
        Args:
            session: Database session
            user_id: ID of the user performing the action
            action: Action being performed
            module: Module/section where action occurred
            description: Detailed description
            entity_type: Type of entity affected
            entity_id: ID of affected entity
            session_id: User session ID
            ip_address: User's IP address
            user_agent: User's browser/client info
            old_values: JSON string of old values (for updates)
            new_values: JSON string of new values (for updates)
            duration_ms: Duration of the action in milliseconds
        """
        try:
            activity = cls(
                user_id=user_id,
                action=action,
                module=module,
                description=description,
                entity_type=entity_type,
                entity_id=entity_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent,
                old_values=old_values,
                new_values=new_values,
                duration_ms=duration_ms
            )
            session.add(activity)
            session.commit()
            return activity
        except Exception as e:
            session.rollback()
            raise e

class UserSession(BaseModel):
    """Model for tracking user sessions."""
    __tablename__ = "user_sessions"
    
    # User reference
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    user = relationship("User")
    
    # Session details
    session_id = Column(String(100), unique=True, nullable=False)
    login_time = Column(DateTime, default=datetime.utcnow, nullable=False)
    logout_time = Column(DateTime, nullable=True)
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Session metadata
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    is_active = Column(String(10), default='active')  # 'active', 'expired', 'logged_out'
    
    def __repr__(self):
        return f"<UserSession {self.session_id} for user {self.user_id}>"
    
    @property
    def duration_minutes(self):
        """Calculate session duration in minutes."""
        end_time = self.logout_time or datetime.utcnow()
        duration = end_time - self.login_time
        return int(duration.total_seconds() / 60)
    
    @classmethod
    def start_session(cls, session, user_id: int, session_id: str, 
                     ip_address: str = None, user_agent: str = None):
        """Start a new user session."""
        try:
            user_session = cls(
                user_id=user_id,
                session_id=session_id,
                ip_address=ip_address,
                user_agent=user_agent
            )
            session.add(user_session)
            session.commit()
            return user_session
        except Exception as e:
            session.rollback()
            raise e
    
    @classmethod
    def end_session(cls, session, session_id: str):
        """End a user session."""
        try:
            user_session = session.query(cls).filter_by(session_id=session_id).first()
            if user_session:
                user_session.logout_time = datetime.utcnow()
                user_session.is_active = 'logged_out'
                session.commit()
            return user_session
        except Exception as e:
            session.rollback()
            raise e
    
    @classmethod
    def update_activity(cls, session, session_id: str):
        """Update last activity time for a session."""
        try:
            user_session = session.query(cls).filter_by(session_id=session_id).first()
            if user_session:
                user_session.last_activity = datetime.utcnow()
                session.commit()
            return user_session
        except Exception as e:
            session.rollback()
            raise e
