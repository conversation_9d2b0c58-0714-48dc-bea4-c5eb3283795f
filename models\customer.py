from sqlalchemy import Column, String, Text, Numeric
from sqlalchemy.orm import relationship
from .base import BaseModel

class Customer(BaseModel):
    """Customer model for storing customer information."""
    __tablename__ = "customers"
    
    # Basic information
    name = Column(String(100), nullable=False, index=True)
    contact_person = Column(String(100), nullable=True)
    
    # Contact information
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    
    # Address information
    address = Column(Text, nullable=True)
    city = Column(String(50), nullable=True)
    state = Column(String(50), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(50), nullable=True)
    
    # Financial information
    commission_rate = Column(Numeric(5, 2), default=0.0)  # Commission rate as percentage (0.00 to 100.00)
    credit_limit = Column(Numeric(15, 2), default=0.0)
    current_balance = Column(Numeric(15, 2), default=0.0)
    
    # Additional information
    notes = Column(Text, nullable=True)
    c_code = Column(String(10), unique=True, nullable=True, index=True)  # Customer code like CNEST-001
    
    # Relationships
    invoices = relationship("Invoice", back_populates="customer")
    
    def __repr__(self):
        return f"<Customer {self.name}>"
    
    @property
    def full_address(self):
        """Return formatted full address."""
        parts = [self.address, self.city, self.state, self.postal_code, self.country]
        return ", ".join(filter(None, parts))
