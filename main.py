#!/usr/bin/env python3
"""
Green Nest Trade Co Accounting Application

A comprehensive accounting and business management solution.
"""
import sys
import os
import traceback
import logging
from pathlib import Path

# Set up basic logging
log_file = Path(__file__).parent / 'logs' / 'app.log'

# Ensure log file directory exists and create an empty log file
log_file.parent.mkdir(parents=True, exist_ok=True)
with open(log_file, 'a'):
    os.utime(log_file, None)  # Touch the file to ensure it exists
print(f"Log file created at: {log_file}")

# Configure root logger
root_logger = logging.getLogger()
root_logger.setLevel(logging.DEBUG)

# File handler
file_handler = logging.FileHandler(log_file, mode='w')
file_handler.setLevel(logging.DEBUG)
file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(file_formatter)

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(levelname)s: %(message)s')
console_handler.setFormatter(console_formatter)

# Add handlers
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

logger = logging.getLogger(__name__)
logger.info("Logging initialized")

def log_unhandled_exception(exc_type, exc_value, exc_traceback):
    """Log unhandled exceptions."""
    if issubclass(exc_type, KeyboardInterrupt):
        # Call the default excepthook for keyboard interrupts
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger.critical("Unhandled exception", exc_info=(exc_type, exc_value, exc_traceback))
    print(f"\nA critical error occurred. Please check the log file: {log_file}")

# Set the exception hook
sys.excepthook = log_unhandled_exception

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for the application."""
    try:
        startup_log = Path(__file__).parent / 'logs' / 'startup_log.txt'
        with open(startup_log, 'w') as f:
            f.write("Starting application initialization\n")
        print("Starting application initialization")
        logger.info("Starting application initialization")
        
        # Import required modules
        try:
            with open(startup_log, 'a') as f:
                f.write("Importing required modules...\n")
            print("Importing required modules...")
            from PyQt6.QtWidgets import QApplication, QMessageBox
            from ui.main_window import MainWindow
            from config import settings
            from database.session import init_db
            with open(startup_log, 'a') as f:
                f.write("Modules imported successfully\n")
            print("Modules imported successfully")
            
            logger.info("Initializing database...")
            with open(startup_log, 'a') as f:
                f.write("Initializing database...\n")
            print("Initializing database...")
            try:
                init_db()
                logger.info("Database initialized successfully")
                with open(startup_log, 'a') as f:
                    f.write("Database initialized successfully\n")
                print("Database initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize database: {e}", exc_info=True)
                with open(startup_log, 'a') as f:
                    f.write(f"Failed to initialize database: {str(e)}\n")
                print(f"Failed to initialize database: {e}")
                raise
            
            logger.info("Creating application instance")
            with open(startup_log, 'a') as f:
                f.write("Creating application instance...\n")
            print("Creating application instance...")
            app = QApplication(sys.argv)
            app.setApplicationName(settings.APP_NAME)
            app.setApplicationVersion(settings.VERSION)
            with open(startup_log, 'a') as f:
                f.write("Application instance created\n")
            print("Application instance created")
            
            logger.info("Creating login window")
            with open(startup_log, 'a') as f:
                f.write("Creating login window...\n")
            print("Creating login window...")
            try:
                from ui.login_window import LoginWindow
                login_window = LoginWindow()
                logger.info("Login window created successfully")
                with open(startup_log, 'a') as f:
                    f.write("Login window created successfully\n")
                print("Login window created successfully")
                login_window.show()
                logger.info("Login window shown")
                with open(startup_log, 'a') as f:
                    f.write("Login window shown\n")
                print("Login window shown")
                
                main_window = None
                
                def handle_login_success():
                    nonlocal main_window
                    logger.info("Login successful, creating main window")
                    with open(startup_log, 'a') as f:
                        f.write("Login successful, creating main window\n")
                    print("Login successful, creating main window")
                    try:
                        main_window = MainWindow()
                        main_window.show()
                        logger.info("Main window shown")
                        with open(startup_log, 'a') as f:
                            f.write("Main window shown\n")
                        print("Main window shown")
                    except Exception as e:
                        logger.critical(f"Error creating main window: {e}", exc_info=True)
                        with open(startup_log, 'a') as f:
                            f.write(f"Error creating main window: {str(e)}\n")
                        print(f"Error creating main window: {e}")
                        app.quit()

                def check_login_success():
                    handle_login_success()
                
                login_window.loginSucceeded.connect(check_login_success)
                login_window.destroyed.connect(lambda: app.quit() if not login_window.login_successful else None)
                
                logger.info("Starting application event loop")
                with open(startup_log, 'a') as f:
                    f.write("Starting application event loop\n")
                print("Starting application event loop")
                return app.exec()
            except Exception as e:
                logger.critical(f"Error creating login window: {e}", exc_info=True)
                with open(startup_log, 'a') as f:
                    f.write(f"Error creating login window: {str(e)}\n")
                print(f"Error creating login window: {e}")
                return 1
            
        except ImportError as e:
            logger.error(f"Import error: {e}", exc_info=True)
            with open(startup_log, 'a') as f:
                f.write(f"Import error: {str(e)}\n")
            print(f"Error: {e}")
            return 1
            
    except Exception as e:
        logger.critical(f"Fatal error: {e}", exc_info=True)
        print(f"\nA fatal error occurred. Please check the log file: {log_file}")
        
        # Show error message if possible
        try:
            app = QApplication.instance() or QApplication(sys.argv)
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Icon.Critical)
            msg.setText("A critical error occurred")
            msg.setInformativeText(str(e))
            msg.setWindowTitle("Error")
            msg.exec()
        except:
            pass
            
        return 1

if __name__ == "__main__":
    sys.exit(main())
