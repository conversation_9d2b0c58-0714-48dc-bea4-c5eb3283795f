"""
Add last_invoice_number column to suppliers table using batch mode for SQLite reliability
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240715_add_last_invoice_number_to_supplier'
down_revision = '20240715_add_unique_invoice_number_per_supplier'
branch_labels = None
depends_on = None

def upgrade():
    with op.batch_alter_table('suppliers', recreate='always') as batch_op:
        batch_op.add_column(sa.Column('last_invoice_number', sa.Integer(), nullable=False, server_default='0'))

def downgrade():
    with op.batch_alter_table('suppliers', recreate='always') as batch_op:
        batch_op.drop_column('last_invoice_number') 