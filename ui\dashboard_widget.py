"""
Main Dashboard Widget
Comprehensive dashboard for Green Nest Trade Co application.
"""
import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QLabel, QPushButton, QFrame, QSizePolicy, QSpacerItem
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont
from ui.dashboard_widgets import <PERSON>ric<PERSON>ard, ChartWidget, DataTableWidget, SystemHealthWidget
from modules.dashboard_analytics import DashboardAnalytics

logger = logging.getLogger(__name__)

class DataRefreshThread(QThread):
    """Thread for refreshing dashboard data without blocking UI."""
    
    data_ready = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        self.analytics = DashboardAnalytics()
        self.running = True
    
    def run(self):
        """Run the data refresh thread."""
        try:
            data = self.analytics.get_all_dashboard_data()
            self.data_ready.emit(data)
        except Exception as e:
            logger.error(f"Error in data refresh thread: {e}")
            self.data_ready.emit({})
    
    def stop(self):
        """Stop the thread."""
        self.running = False
        self.quit()
        self.wait()

class DashboardWidget(QWidget):
    """Main dashboard widget with comprehensive analytics."""

    def __init__(self):
        super().__init__()
        self.analytics = DashboardAnalytics()
        self.refresh_thread = None
        self.is_compact_mode = False
        self.setup_ui()
        self.setup_refresh_timer()
        self.refresh_data()

        # Force initial responsive layout adjustment
        self.force_responsive_update()
    
    def setup_ui(self):
        """Set up the dashboard UI with improved typography and spacing."""
        # Main layout with scroll area
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(25)

        # Header with improved styling
        header_layout = QHBoxLayout()

        title_label = QLabel("Dashboard")
        title_label.setFont(QFont("Segoe UI", 32, QFont.Weight.Bold))
        title_label.setStyleSheet("""
            color: #FFFFFF;
            margin-bottom: 15px;
            line-height: 1.2;
            letter-spacing: 0.5px;
        """)
        header_layout.addWidget(title_label)

        # Test data indicator
        test_indicator = QLabel("📊 DEMO DATA - For Testing & Demonstration Purposes")
        test_indicator.setFont(QFont("Segoe UI", 11, QFont.Weight.Bold))
        test_indicator.setStyleSheet("""
            color: #f39c12;
            background-color: #2C2C2C;
            border: 2px solid #f39c12;
            border-radius: 4px;
            padding: 6px 12px;
            margin-left: 20px;
        """)
        header_layout.addWidget(test_indicator)
        
        header_layout.addStretch()
        
        # Refresh button
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.refresh_button.clicked.connect(self.refresh_data)
        header_layout.addWidget(self.refresh_button)
        
        # Last updated label
        self.last_updated_label = QLabel("Last updated: Never")
        self.last_updated_label.setStyleSheet("color: #CCCCCC; font-size: 11px;")
        header_layout.addWidget(self.last_updated_label)
        
        main_layout.addLayout(header_layout)
        
        # Scroll area for dashboard content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #404040;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #606060;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #707070;
            }
        """)
        
        # Dashboard content widget with improved spacing
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(30)

        # Set responsive minimum width for content
        content_widget.setMinimumWidth(900)
        
        # Key metrics row
        self.create_metrics_section(content_layout)
        
        # Charts row
        self.create_charts_section(content_layout)
        
        # Tables and system health row
        self.create_tables_section(content_layout)

        # User activity section
        self.create_activity_section(content_layout)
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
    
    def create_metrics_section(self, parent_layout):
        """Create the key metrics section with improved responsiveness."""
        metrics_frame = QFrame()
        metrics_frame.setStyleSheet("QFrame { background-color: transparent; }")

        # Use grid layout for better responsiveness
        self.metrics_layout = QGridLayout(metrics_frame)
        self.metrics_layout.setContentsMargins(10, 10, 10, 10)
        self.metrics_layout.setSpacing(20)

        # Create metric cards with better sizing and colors
        self.revenue_card = MetricCard("Total Revenue (30 days)", "$0.00", "Last 30 days", "#2ecc71")
        self.invoices_card = MetricCard("Total Invoices", "0", "All invoices", "#3498db")
        self.customers_card = MetricCard("Active Customers", "0", "Customer base", "#e74c3c")
        self.overdue_card = MetricCard("Overdue Invoices", "0", "Needs attention", "#f39c12")

        # Store cards for easy access
        self.metric_cards = [self.revenue_card, self.invoices_card, self.customers_card, self.overdue_card]

        # Set responsive sizing for cards
        for card in self.metric_cards:
            card.setMinimumWidth(220)
            card.setMaximumWidth(350)
            card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)

        # Initial layout - will be adjusted in adjust_layout_for_size
        self.arrange_metric_cards()

        parent_layout.addWidget(metrics_frame)

    def arrange_metric_cards(self):
        """Arrange metric cards based on current layout mode."""
        # Clear existing layout
        for i in reversed(range(self.metrics_layout.count())):
            self.metrics_layout.itemAt(i).widget().setParent(None)

        if self.is_compact_mode:
            # Compact mode: 2x2 grid
            self.metrics_layout.addWidget(self.revenue_card, 0, 0)
            self.metrics_layout.addWidget(self.invoices_card, 0, 1)
            self.metrics_layout.addWidget(self.customers_card, 1, 0)
            self.metrics_layout.addWidget(self.overdue_card, 1, 1)

            # Set column and row stretch
            for i in range(2):
                self.metrics_layout.setColumnStretch(i, 1)
                self.metrics_layout.setRowStretch(i, 0)
        else:
            # Full mode: 1x4 grid
            self.metrics_layout.addWidget(self.revenue_card, 0, 0)
            self.metrics_layout.addWidget(self.invoices_card, 0, 1)
            self.metrics_layout.addWidget(self.customers_card, 0, 2)
            self.metrics_layout.addWidget(self.overdue_card, 0, 3)

            # Set column stretch to make cards expand evenly
            for i in range(4):
                self.metrics_layout.setColumnStretch(i, 1)
    
    def create_charts_section(self, parent_layout):
        """Create the charts section."""
        charts_frame = QFrame()
        charts_frame.setStyleSheet("QFrame { background-color: transparent; }")
        charts_layout = QHBoxLayout(charts_frame)
        charts_layout.setContentsMargins(5, 5, 5, 5)
        charts_layout.setSpacing(15)

        # Sales trend chart
        self.sales_chart = ChartWidget("Sales Trend (30 Days)")
        self.sales_chart.setMinimumHeight(300)
        self.sales_chart.setMinimumWidth(400)
        self.sales_chart.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        charts_layout.addWidget(self.sales_chart, 1)  # Give it stretch factor of 1

        # Invoice status chart
        self.status_chart = ChartWidget("Invoice Status Distribution")
        self.status_chart.setMinimumHeight(300)
        self.status_chart.setMinimumWidth(400)
        self.status_chart.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        charts_layout.addWidget(self.status_chart, 1)  # Give it stretch factor of 1

        parent_layout.addWidget(charts_frame)
    
    def create_tables_section(self, parent_layout):
        """Create the tables and system health section."""
        tables_frame = QFrame()
        tables_frame.setStyleSheet("QFrame { background-color: transparent; }")
        tables_layout = QHBoxLayout(tables_frame)
        tables_layout.setContentsMargins(5, 5, 5, 5)
        tables_layout.setSpacing(15)

        # Left column - Tables
        left_column = QVBoxLayout()
        left_column.setSpacing(15)

        # Top customers table
        self.customers_table = DataTableWidget(
            "Top Customers",
            ["name", "code", "total_revenue", "invoice_count", "last_invoice_date"]
        )
        self.customers_table.setMinimumHeight(200)
        self.customers_table.setMaximumHeight(300)
        self.customers_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        left_column.addWidget(self.customers_table)

        # Recent transactions table
        self.transactions_table = DataTableWidget(
            "Recent Transactions",
            ["invoice_number", "entity_name", "entity_type", "amount", "status", "date"]
        )
        self.transactions_table.setMinimumHeight(200)
        self.transactions_table.setMaximumHeight(300)
        self.transactions_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        left_column.addWidget(self.transactions_table)

        # Right column - System health and suppliers
        right_column = QVBoxLayout()
        right_column.setSpacing(15)

        # System health widget
        self.system_health = SystemHealthWidget()
        self.system_health.setMinimumHeight(180)
        self.system_health.setMaximumHeight(220)
        self.system_health.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        right_column.addWidget(self.system_health)

        # Top suppliers table
        self.suppliers_table = DataTableWidget(
            "Top Suppliers",
            ["name", "code", "total_amount", "invoice_count", "last_invoice_date"]
        )
        self.suppliers_table.setMinimumHeight(200)
        self.suppliers_table.setMaximumHeight(300)
        self.suppliers_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        right_column.addWidget(self.suppliers_table)

        # Add columns to main layout with better proportions
        left_widget = QWidget()
        left_widget.setLayout(left_column)
        left_widget.setMinimumWidth(400)
        left_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        right_widget = QWidget()
        right_widget.setLayout(right_column)
        right_widget.setMinimumWidth(300)
        right_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        tables_layout.addWidget(left_widget, 3)  # 60% width
        tables_layout.addWidget(right_widget, 2)  # 40% width

        parent_layout.addWidget(tables_frame)

    def create_activity_section(self, parent_layout):
        """Create the user activity section."""
        activity_frame = QFrame()
        activity_frame.setStyleSheet("QFrame { background-color: transparent; }")
        activity_layout = QHBoxLayout(activity_frame)
        activity_layout.setContentsMargins(5, 5, 5, 5)
        activity_layout.setSpacing(15)

        # Recent activities table
        self.activities_table = DataTableWidget(
            "Recent User Activities",
            ["action", "module", "description", "user_id", "created_at"]
        )
        self.activities_table.setMinimumHeight(200)
        self.activities_table.setMaximumHeight(280)
        self.activities_table.setMinimumWidth(400)
        self.activities_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        activity_layout.addWidget(self.activities_table, 3)  # 60% width

        # Activity statistics
        stats_widget = QWidget()
        stats_widget.setMinimumWidth(250)
        stats_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        stats_layout = QVBoxLayout(stats_widget)
        stats_layout.setSpacing(15)

        # Active sessions metric
        self.active_sessions_card = MetricCard("Active Sessions", "0", "Current users", "#9b59b6")
        self.active_sessions_card.setMinimumWidth(200)
        self.active_sessions_card.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        stats_layout.addWidget(self.active_sessions_card)

        # Most active users table
        self.active_users_table = DataTableWidget(
            "Most Active Users",
            ["username", "full_name", "activity_count"]
        )
        self.active_users_table.setMinimumHeight(150)
        self.active_users_table.setMaximumHeight(200)
        self.active_users_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        stats_layout.addWidget(self.active_users_table)

        activity_layout.addWidget(stats_widget, 2)  # 40% width
        parent_layout.addWidget(activity_frame)
    
    def setup_refresh_timer(self):
        """Set up automatic refresh timer."""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # Refresh every 60 seconds
    
    def refresh_data(self):
        """Refresh dashboard data."""
        if self.refresh_thread and self.refresh_thread.isRunning():
            return
        
        self.refresh_button.setText("Refreshing...")
        self.refresh_button.setEnabled(False)
        
        # Start data refresh in background thread
        self.refresh_thread = DataRefreshThread()
        self.refresh_thread.data_ready.connect(self.update_dashboard)
        self.refresh_thread.start()
    
    @pyqtSlot(dict)
    def update_dashboard(self, data: Dict[str, Any]):
        """Update dashboard with new data."""
        try:
            if not data:
                logger.warning("No data received for dashboard update")
                return
            
            # Update metrics cards
            sales_metrics = data.get('sales_metrics', {})
            revenue_value = f"${sales_metrics.get('total_revenue', 0):,.2f}"
            if sales_metrics.get('is_test_data', False):
                revenue_value += " 🧪"
            self.revenue_card.update_value(revenue_value)

            invoice_value = str(sales_metrics.get('invoice_count', 0))
            if sales_metrics.get('is_test_data', False):
                invoice_value += " 🧪"
            self.invoices_card.update_value(invoice_value)
            
            invoice_status = data.get('invoice_status', {})
            self.overdue_card.update_value(str(invoice_status.get('overdue_count', 0)))
            
            # Customer count from system health
            system_health = data.get('system_health', {})
            record_counts = system_health.get('record_counts', {})
            self.customers_card.update_value(str(record_counts.get('customers', 0)))
            
            # Update charts
            daily_sales = sales_metrics.get('daily_sales', [])
            if daily_sales:
                chart_title = 'Daily Revenue'
                if sales_metrics.get('is_test_data', False):
                    chart_title += ' (Sample Data)'
                    self.sales_chart.test_indicator.show()
                else:
                    self.sales_chart.test_indicator.hide()
                self.sales_chart.plot_line_chart(daily_sales, 'date', 'revenue', chart_title)

            status_distribution = invoice_status.get('status_distribution', [])
            if status_distribution:
                chart_title = 'Invoice Status'
                if invoice_status.get('is_test_data', False):
                    chart_title += ' (Sample Data)'
                    self.status_chart.test_indicator.show()
                else:
                    self.status_chart.test_indicator.hide()
                self.status_chart.plot_pie_chart(status_distribution, 'status', 'count', chart_title)
            
            # Update tables
            top_customers = data.get('top_customers', [])
            self.customers_table.update_data(top_customers)
            
            top_suppliers = data.get('top_suppliers', [])
            self.suppliers_table.update_data(top_suppliers)
            
            recent_transactions = data.get('recent_transactions', [])
            self.transactions_table.update_data(recent_transactions)
            
            # Update system health
            self.system_health.update_health_data(system_health)

            # Update user activity data
            user_activity = data.get('user_activity', {})
            recent_activities = user_activity.get('recent_activities', [])
            self.activities_table.update_data(recent_activities)

            active_sessions = user_activity.get('active_sessions', 0)
            self.active_sessions_card.update_value(str(active_sessions))

            most_active_users = user_activity.get('most_active_users', [])
            self.active_users_table.update_data(most_active_users)

            # Update last updated time
            last_updated = data.get('last_updated', 'Unknown')
            self.last_updated_label.setText(f"Last updated: {last_updated}")
            
        except Exception as e:
            logger.error(f"Error updating dashboard: {e}")
        finally:
            self.refresh_button.setText("Refresh")
            self.refresh_button.setEnabled(True)
    
    def resizeEvent(self, event):
        """Handle widget resize event for responsive design."""
        super().resizeEvent(event)

        # Determine if we should use compact mode based on width with better breakpoints
        new_width = event.size().width()
        should_be_compact = new_width < 1400  # Increased threshold for better responsiveness

        if should_be_compact != self.is_compact_mode:
            self.is_compact_mode = should_be_compact
            # Only adjust layout if widgets are initialized
            if hasattr(self, 'metric_cards') and self.metric_cards:
                self.adjust_layout_for_size()

    def adjust_layout_for_size(self):
        """Adjust layout based on current size with improved responsive design."""
        # Update metric cards layout
        if hasattr(self, 'metrics_layout'):
            self.arrange_metric_cards()

        # Set compact mode for all metric cards
        if hasattr(self, 'metric_cards') and self.metric_cards:
            for card in self.metric_cards:
                card.set_compact_mode(self.is_compact_mode)

        if self.is_compact_mode:
            # Compact mode adjustments for smaller screens
            for card in self.metric_cards:
                card.setMaximumWidth(280)

            # Adjust chart sizes for compact mode
            self.sales_chart.setMinimumHeight(220)
            self.status_chart.setMinimumHeight(220)

            # Adjust chart canvas minimum size
            if hasattr(self.sales_chart, 'canvas'):
                self.sales_chart.canvas.setMinimumHeight(220)
            if hasattr(self.status_chart, 'canvas'):
                self.status_chart.canvas.setMinimumHeight(220)

            # Adjust table heights for compact mode
            self.customers_table.setMaximumHeight(250)
            self.transactions_table.setMaximumHeight(250)
            self.suppliers_table.setMaximumHeight(250)
            self.activities_table.setMaximumHeight(250)
        else:
            # Full mode adjustments for larger screens
            for card in self.metric_cards:
                card.setMaximumWidth(350)

            # Adjust chart sizes for full mode
            self.sales_chart.setMinimumHeight(300)
            self.status_chart.setMinimumHeight(300)

            # Adjust chart canvas minimum size
            if hasattr(self.sales_chart, 'canvas'):
                self.sales_chart.canvas.setMinimumHeight(300)
            if hasattr(self.status_chart, 'canvas'):
                self.status_chart.canvas.setMinimumHeight(300)

            # Adjust table heights for full mode
            self.customers_table.setMaximumHeight(350)
            self.transactions_table.setMaximumHeight(350)
            self.suppliers_table.setMaximumHeight(350)
            self.activities_table.setMaximumHeight(330)

    def force_responsive_update(self):
        """Force a responsive layout update based on current size."""
        if hasattr(self, 'metric_cards') and self.metric_cards:
            current_width = self.width()
            should_be_compact = current_width < 1400
            if should_be_compact != self.is_compact_mode:
                self.is_compact_mode = should_be_compact
                self.adjust_layout_for_size()

    def closeEvent(self, event):
        """Handle widget close event."""
        if self.refresh_thread:
            self.refresh_thread.stop()
        if hasattr(self, 'refresh_timer'):
            self.refresh_timer.stop()
        self.analytics.close_session()
        event.accept()
