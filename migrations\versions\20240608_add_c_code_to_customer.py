"""
Add c_code column to customers table (no unique/index for SQLite compatibility)
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20240608_add_c_code_to_customer'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    op.add_column('customers', sa.Column('c_code', sa.String(10), nullable=True))
    op.add_column('invoices', sa.Column('commission_rate', sa.Numeric(5, 2), nullable=False, server_default='0.0'))
    op.add_column('invoices', sa.Column('commission_amount', sa.Numeric(15, 2), nullable=False, server_default='0.0'))


def downgrade():
    op.drop_column('customers', 'c_code')
    op.drop_column('invoices', 'commission_rate')
    op.drop_column('invoices', 'commission_amount')
