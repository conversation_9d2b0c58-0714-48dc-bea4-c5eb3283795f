import logging
from PyQt6.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
                             QPushButton, QMessageBox, QMainWindow)
from PyQt6.QtCore import Qt, QSize, pyqtSignal
from PyQt6.QtGui import QPixmap, QIcon
from pathlib import Path
from utils.security import get_password_hash, verify_password
from config import settings
from ui.dark_theme import get_dark_theme_stylesheet

# Set up logger
logger = logging.getLogger(__name__)

class LoginWindow(QMainWindow):
    loginSucceeded = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"Login - {settings.APP_NAME}")
        self.setFixedSize(400, 500)
        self.setup_ui()
        
        # Hardcoded credentials
        self.username = "cyrus"
        self.password_hash = get_password_hash("cyruscyrus")
        self.login_successful = False
        
    def setup_ui(self):
        """Set up the login user interface."""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Add logo
        logo_label = QLabel()
        logo_path = Path("assets/Green Nest Trade Co logo.png")
        if logo_path.exists():
            pixmap = QPixmap(str(logo_path))
            scaled_pixmap = pixmap.scaled(QSize(200, 200), Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
            logo_label.setPixmap(scaled_pixmap)
            logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        else:
            logo_label.setText("Green Nest Trade Co")
            logo_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #FFFFFF;")
            logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            logger.warning(f"Logo file not found at: {logo_path}")
        
        main_layout.addWidget(logo_label)
        
        # Add title
        title_label = QLabel("Login to Your Account")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #FFFFFF;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Username field
        username_widget = QWidget()
        username_layout = QHBoxLayout(username_widget)
        username_layout.setContentsMargins(0, 0, 0, 0)
        username_label = QLabel("Username:")
        username_label.setStyleSheet("font-size: 14px; color: #FFFFFF;")
        username_label.setFixedWidth(80)
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Enter username")
        self.username_input.setStyleSheet("padding: 8px; font-size: 14px; border-radius: 4px; background-color: #2C2C2C; color: #FFFFFF; border: 1px solid #3A3A3A;")
        username_layout.addWidget(username_label)
        username_layout.addWidget(self.username_input)
        main_layout.addWidget(username_widget)
        
        # Password field
        password_widget = QWidget()
        password_layout = QHBoxLayout(password_widget)
        password_layout.setContentsMargins(0, 0, 0, 0)
        password_label = QLabel("Password:")
        password_label.setStyleSheet("font-size: 14px; color: #FFFFFF;")
        password_label.setFixedWidth(80)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("Enter password")
        self.password_input.setStyleSheet("padding: 8px; font-size: 14px; border-radius: 4px; background-color: #2C2C2C; color: #FFFFFF; border: 1px solid #3A3A3A;")
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        main_layout.addWidget(password_widget)
        
        # Login button
        login_button = QPushButton("Login")
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 16px;
                font-weight: bold;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        login_button.clicked.connect(self.handle_login)
        main_layout.addWidget(login_button)
        
        # Add stretch to push content to center
        main_layout.addStretch()
        
        # Apply dark theme stylesheet
        stylesheet = get_dark_theme_stylesheet()
        self.setStyleSheet(stylesheet)
        
    def handle_login(self):
        """Handle login button click."""
        entered_username = self.username_input.text().strip()
        entered_password = self.password_input.text()
        
        if not entered_username or not entered_password:
            QMessageBox.warning(self, "Login Failed", "Please enter both username and password.")
            return
        
        if entered_username == self.username and verify_password(entered_password, self.password_hash):
            QMessageBox.information(self, "Login Successful", "Welcome to Green Nest Trade Co App.")
            self.accept_login()
        else:
            QMessageBox.critical(self, "Login Failed", "Invalid username or password.")
            logger.warning(f"Failed login attempt with username: {entered_username}")
    
    def keyPressEvent(self, event):
        """Handle key press events for Enter key login."""
        if event.key() in (Qt.Key.Key_Return, Qt.Key.Key_Enter):
            self.handle_login()
        super().keyPressEvent(event)

    def accept_login(self):
        """Signal that login was successful."""
        self.login_successful = True
        self.hide()
        self.loginSucceeded.emit()
    
    def closeEvent(self, event):
        """Handle window close event."""
        if self.login_successful:
            event.accept()
        else:
            reply = QMessageBox.question(
                self, 'Exit',
                'Are you sure you want to exit?',
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                event.accept()
            else:
                event.ignore()
