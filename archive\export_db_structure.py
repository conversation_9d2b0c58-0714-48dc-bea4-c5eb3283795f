import pyodbc
import os
from datetime import datetime

def get_connection(db_path, username='', password=''):
    """Establish a connection to the Access database."""
    conn_strs = [
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};UID={username};PWD={password};',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path};UID={username};PWD={password};',
    ]
    
    for conn_str in conn_strs:
        try:
            conn = pyodbc.connect(conn_str)
            print(f"Successfully connected to the database.")
            return conn
        except pyodbc.Error as e:
            continue
    
    print("Failed to connect to the database with any connection string.")
    return None

def generate_html_report(db_path, output_file):
    """Generate an HTML report of the database structure."""
    conn = get_connection(db_path, "majid", "majid")
    if not conn:
        print("Could not connect to the database.")
        return
    
    try:
        cursor = conn.cursor()
        
        # Get all tables
        tables = []
        for table in cursor.tables(tableType='TABLE'):
            table_name = table.table_name
            if not table_name.startswith('MSys'):
                tables.append(table_name)
        
        # Sort tables
        tables = sorted(tables)
        
        # Start HTML content
        html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Database Structure Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        h1, h2, h3 {{ color: #2c3e50; }}
        table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
        .table-container {{ margin-bottom: 40px; }}
        .sample-data {{ font-family: monospace; white-space: pre; }}
        .toc {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Structure Report</h1>
        <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        <p>Database: {os.path.basename(db_path)}</p>
        
        <div class="toc">
            <h2>Table of Contents</h2>
            <ul>
"""
        
        # Add table of contents
        for i, table in enumerate(tables, 1):
            html += f'<li><a href="#table{i}">{table}</a></li>\n'
        html += """
            </ul>
        </div>
"""
        
        # Add table details
        for i, table in enumerate(tables, 1):
            html += f'<div class="table-container" id="table{i}">\n'
            # Get column information
            columns = []
            try:
                for col in cursor.columns(table=table):
                    columns.append({
                        'name': col.column_name,
                        'type': col.type_name,
                        'size': col.column_size,
                        'nullable': col.nullable == 1
                    })
            except Exception as e:
                html += f'<p>Error getting columns: {e}</p>'
                continue
            
            # Table header
            html += f'<h2>{table} <small>({len(columns)} columns)</small></h2>\n'
            # Columns table
            html += '''
            <table>
                <tr>
                    <th>Column Name</th>
                    <th>Data Type</th>
                    <th>Size</th>
                    <th>Nullable</th>
                </tr>
'''
            
            for col in columns:
                html += f'''
                <tr>
                    <td><strong>{col['name']}</strong></td>
                    <td>{col['type']}</td>
                    <td>{col['size'] or '-'}</td>
                    <td>{'Yes' if col['nullable'] else 'No'}</td>
                </tr>
'''
            
            html += '</table>\n'
            # Add sample data
            try:
                cursor.execute(f'SELECT TOP 5 * FROM [{table}]')
                rows = cursor.fetchall()
                
                if rows:
                    html += '<h3>Sample Data (first 5 rows)</h3>\n'
                    # Get column names
                    col_names = [column[0] for column in cursor.description]
                    
                    html += '<table>'
                    # Header row
                    html += '<tr>'
                    for name in col_names:
                        html += f'<th>{name}</th>'
                    html += '</tr>'
                    
                    # Data rows
                    for row in rows:
                        html += '<tr>'
                        for value in row:
                            # Handle None values and long strings
                            display_value = str(value) if value is not None else 'NULL'
                            if len(display_value) > 50:
                                display_value = display_value[:47] + '...'
                            html += f'<td>{display_value}</td>'
                        html += '</tr>'
                    
                    html += '</table>'
                    
            except Exception as e:
                html += f'<p>Could not retrieve sample data: {e}</p>'
            
            html += '</div>\n'
        # Close HTML
        html += """
        </div>
    </body>
</html>
"""
        
        # Write to file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html)
        
        print(f"Report generated: {os.path.abspath(output_file)}")
            
    except Exception as e:
        print(f"An error occurred: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    output_file = "database_structure.html"
    
    if not os.path.exists(db_path):
        print(f"Error: Database file not found at {db_path}")
    else:
        generate_html_report(db_path, output_file)
        print("\nYou can now open the generated HTML file in your web browser to view the database structure.")
