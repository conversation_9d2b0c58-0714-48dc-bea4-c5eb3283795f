"""
Dashboard Analytics Service
Provides data analytics and metrics for the dashboard.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
from database.session import SessionLocal
from models.customer import Customer
from models.supplier import Supplier
from models.invoice import Invoice, InvoiceStatus
from models.product import Product
from models.user import User
from models.user_activity import UserActivity, UserSession
import psutil
import time

logger = logging.getLogger(__name__)

class DashboardAnalytics:
    """Service class for dashboard analytics and metrics."""
    
    def __init__(self):
        self.db: Optional[Session] = None
        self._last_refresh = None
        self._cache_duration = 30  # seconds
        self._cached_data = {}
    
    def get_session(self) -> Session:
        """Get database session."""
        if not self.db:
            self.db = SessionLocal()
        return self.db
    
    def close_session(self):
        """Close database session."""
        if self.db:
            self.db.close()
            self.db = None
    
    def _should_refresh_cache(self) -> bool:
        """Check if cache should be refreshed."""
        if not self._last_refresh:
            return True
        return (datetime.now() - self._last_refresh).seconds > self._cache_duration
    
    def get_sales_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Get sales metrics for the specified number of days."""
        try:
            db = self.get_session()
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Total revenue
            total_revenue = db.query(func.sum(Invoice.total_amount)).filter(
                and_(
                    Invoice.invoice_date >= start_date,
                    Invoice.invoice_date <= end_date,
                    Invoice.status != InvoiceStatus.CANCELLED
                )
            ).scalar() or 0

            # Invoice count
            invoice_count = db.query(func.count(Invoice.id)).filter(
                and_(
                    Invoice.invoice_date >= start_date,
                    Invoice.invoice_date <= end_date,
                    Invoice.status != InvoiceStatus.CANCELLED
                )
            ).scalar() or 0

            # Average invoice value
            avg_invoice_value = total_revenue / invoice_count if invoice_count > 0 else 0

            # Daily sales data for charts
            daily_sales = db.query(
                func.date(Invoice.invoice_date).label('date'),
                func.sum(Invoice.total_amount).label('revenue'),
                func.count(Invoice.id).label('count')
            ).filter(
                and_(
                    Invoice.invoice_date >= start_date,
                    Invoice.invoice_date <= end_date,
                    Invoice.status != InvoiceStatus.CANCELLED
                )
            ).group_by(func.date(Invoice.invoice_date)).all()

            # If no real data, provide sample test data
            if not daily_sales:
                daily_sales = self._generate_sample_daily_sales(days)
                total_revenue = sum(day['revenue'] for day in daily_sales)
                invoice_count = sum(day['count'] for day in daily_sales)
                avg_invoice_value = total_revenue / invoice_count if invoice_count > 0 else 0

                return {
                    'total_revenue': float(total_revenue),
                    'invoice_count': invoice_count,
                    'average_invoice_value': float(avg_invoice_value),
                    'daily_sales': daily_sales,
                    'is_test_data': True,
                    'data_note': 'Sample data for demonstration - Replace with real business data'
                }

            return {
                'total_revenue': float(total_revenue),
                'invoice_count': invoice_count,
                'average_invoice_value': float(avg_invoice_value),
                'daily_sales': [
                    {
                        'date': str(row.date),
                        'revenue': float(row.revenue),
                        'count': row.count
                    } for row in daily_sales
                ],
                'is_test_data': False
            }
        except Exception as e:
            logger.error(f"Error getting sales metrics: {e}")
            # Return sample data on error
            daily_sales = self._generate_sample_daily_sales(days)
            total_revenue = sum(day['revenue'] for day in daily_sales)
            invoice_count = sum(day['count'] for day in daily_sales)

            return {
                'total_revenue': float(total_revenue),
                'invoice_count': invoice_count,
                'average_invoice_value': float(total_revenue / invoice_count if invoice_count > 0 else 0),
                'daily_sales': daily_sales,
                'is_test_data': True,
                'data_note': 'Sample data for demonstration - Database connection issue'
            }

    def _generate_sample_daily_sales(self, days: int) -> List[Dict[str, Any]]:
        """Generate sample daily sales data for demonstration."""
        import random

        daily_sales = []
        base_date = datetime.now() - timedelta(days=days)

        for i in range(days):
            date = base_date + timedelta(days=i)
            # Generate realistic sample data
            revenue = random.uniform(1000, 5000)
            count = random.randint(3, 15)

            daily_sales.append({
                'date': date.strftime('%Y-%m-%d'),
                'revenue': round(revenue, 2),
                'count': count
            })

        return daily_sales
    
    def get_invoice_status_metrics(self) -> Dict[str, Any]:
        """Get invoice status distribution."""
        try:
            db = self.get_session()
            
            status_counts = db.query(
                Invoice.status,
                func.count(Invoice.id).label('count'),
                func.sum(Invoice.total_amount).label('total_amount')
            ).group_by(Invoice.status).all()
            
            # Calculate overdue invoices
            overdue_count = db.query(func.count(Invoice.id)).filter(
                and_(
                    Invoice.due_date < datetime.now(),
                    Invoice.is_paid == False,
                    Invoice.status != InvoiceStatus.CANCELLED
                )
            ).scalar() or 0
            
            return {
                'status_distribution': [
                    {
                        'status': row.status.value if row.status else 'Unknown',
                        'count': row.count,
                        'total_amount': float(row.total_amount or 0)
                    } for row in status_counts
                ],
                'overdue_count': overdue_count
            }
        except Exception as e:
            logger.error(f"Error getting invoice status metrics: {e}")
            return {'status_distribution': [], 'overdue_count': 0}
    
    def get_top_customers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top customers by revenue."""
        try:
            db = self.get_session()

            top_customers = db.query(
                Customer.name,
                Customer.c_code,
                func.sum(Invoice.total_amount).label('total_revenue'),
                func.count(Invoice.id).label('invoice_count'),
                func.max(Invoice.invoice_date).label('last_invoice_date')
            ).join(Invoice).filter(
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Customer.id).order_by(
                desc('total_revenue')
            ).limit(limit).all()

            customers_data = [
                {
                    'name': row.name,
                    'code': row.c_code,
                    'total_revenue': float(row.total_revenue),
                    'invoice_count': row.invoice_count,
                    'last_invoice_date': row.last_invoice_date.strftime('%Y-%m-%d') if row.last_invoice_date else None
                } for row in top_customers
            ]

            # If no real data, provide sample test data
            if not customers_data:
                customers_data = self._generate_sample_customers(limit)
                # Add test data indicator
                for customer in customers_data:
                    customer['is_test_data'] = True

            return customers_data

        except Exception as e:
            logger.error(f"Error getting top customers: {e}")
            # Return sample data on error
            return self._generate_sample_customers(limit)

    def _generate_sample_customers(self, limit: int) -> List[Dict[str, Any]]:
        """Generate sample customer data for demonstration."""
        import random

        sample_customers = [
            "ABC Manufacturing Corp", "XYZ Trading Ltd", "Global Supply Co",
            "Metro Business Solutions", "Prime Industries Inc", "Elite Commerce Group",
            "Sunrise Enterprises", "Pacific Trade Partners", "Diamond Logistics",
            "Sterling Business Corp", "Apex Commercial Ltd", "Unity Trading Co"
        ]

        customers = []
        for i in range(min(limit, len(sample_customers))):
            customers.append({
                'name': f"{sample_customers[i]} [TEST DATA]",
                'code': f"CUST{1000 + i}",
                'total_revenue': round(random.uniform(5000, 50000), 2),
                'invoice_count': random.randint(5, 25),
                'last_invoice_date': (datetime.now() - timedelta(days=random.randint(1, 30))).strftime('%Y-%m-%d'),
                'is_test_data': True
            })

        return sorted(customers, key=lambda x: x['total_revenue'], reverse=True)
    
    def get_top_suppliers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top suppliers by transaction volume."""
        try:
            db = self.get_session()

            top_suppliers = db.query(
                Supplier.name,
                Supplier.s_code,
                func.sum(Invoice.total_amount).label('total_amount'),
                func.count(Invoice.id).label('invoice_count'),
                func.max(Invoice.invoice_date).label('last_invoice_date')
            ).join(Invoice).filter(
                Invoice.status != InvoiceStatus.CANCELLED
            ).group_by(Supplier.id).order_by(
                desc('total_amount')
            ).limit(limit).all()

            suppliers_data = [
                {
                    'name': row.name,
                    'code': row.s_code,
                    'total_amount': float(row.total_amount),
                    'invoice_count': row.invoice_count,
                    'last_invoice_date': row.last_invoice_date.strftime('%Y-%m-%d') if row.last_invoice_date else None
                } for row in top_suppliers
            ]

            # If no real data, provide sample test data
            if not suppliers_data:
                suppliers_data = self._generate_sample_suppliers(limit)

            return suppliers_data

        except Exception as e:
            logger.error(f"Error getting top suppliers: {e}")
            return self._generate_sample_suppliers(limit)

    def _generate_sample_suppliers(self, limit: int) -> List[Dict[str, Any]]:
        """Generate sample supplier data for demonstration."""
        import random

        sample_suppliers = [
            "TechParts Wholesale", "Industrial Supply Co", "Quality Components Ltd",
            "Reliable Materials Inc", "Premium Parts Group", "Express Logistics",
            "Global Sourcing Partners", "Efficient Supply Chain", "Direct Import Co",
            "Bulk Materials Corp", "Specialty Products Ltd", "Fast Track Suppliers"
        ]

        suppliers = []
        for i in range(min(limit, len(sample_suppliers))):
            suppliers.append({
                'name': f"{sample_suppliers[i]} [TEST DATA]",
                'code': f"SUPP{2000 + i}",
                'total_amount': round(random.uniform(3000, 40000), 2),
                'invoice_count': random.randint(3, 20),
                'last_invoice_date': (datetime.now() - timedelta(days=random.randint(1, 45))).strftime('%Y-%m-%d'),
                'is_test_data': True
            })

        return sorted(suppliers, key=lambda x: x['total_amount'], reverse=True)
    
    def get_recent_transactions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent transactions."""
        try:
            db = self.get_session()

            recent_invoices = db.query(Invoice).order_by(
                desc(Invoice.created_at)
            ).limit(limit).all()

            transactions = []
            for invoice in recent_invoices:
                entity_name = "Unknown"
                entity_type = "Unknown"

                if invoice.customer:
                    entity_name = invoice.customer.name
                    entity_type = "Customer"
                elif invoice.supplier:
                    entity_name = invoice.supplier.name
                    entity_type = "Supplier"

                transactions.append({
                    'invoice_number': invoice.invoice_number,
                    'entity_name': entity_name,
                    'entity_type': entity_type,
                    'amount': float(invoice.total_amount),
                    'status': invoice.status.value if invoice.status else 'Unknown',
                    'date': invoice.invoice_date.strftime('%Y-%m-%d %H:%M') if invoice.invoice_date else None,
                    'is_paid': invoice.is_paid
                })

            # If no real data, provide sample test data
            if not transactions:
                transactions = self._generate_sample_transactions(limit)

            return transactions
        except Exception as e:
            logger.error(f"Error getting recent transactions: {e}")
            return self._generate_sample_transactions(limit)

    def _generate_sample_transactions(self, limit: int) -> List[Dict[str, Any]]:
        """Generate sample transaction data for demonstration."""
        import random

        statuses = ['DRAFT', 'SENT', 'PAID', 'OVERDUE']
        entity_types = ['Customer', 'Supplier']

        transactions = []
        for i in range(limit):
            entity_type = random.choice(entity_types)
            entity_name = f"Sample {entity_type} {i+1} [TEST DATA]"

            transactions.append({
                'invoice_number': f"INV-TEST-{3000 + i}",
                'entity_name': entity_name,
                'entity_type': entity_type,
                'amount': round(random.uniform(500, 8000), 2),
                'status': random.choice(statuses),
                'date': (datetime.now() - timedelta(days=random.randint(0, 14))).strftime('%Y-%m-%d %H:%M'),
                'is_paid': random.choice([True, False]),
                'is_test_data': True
            })

        return sorted(transactions, key=lambda x: x['date'], reverse=True)
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics."""
        try:
            # CPU and Memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Database connection test
            db_status = "Connected"
            try:
                db = self.get_session()
                db.execute("SELECT 1")
            except Exception:
                db_status = "Disconnected"
            
            # Count database records
            db = self.get_session()
            customer_count = db.query(func.count(Customer.id)).scalar() or 0
            supplier_count = db.query(func.count(Supplier.id)).scalar() or 0
            invoice_count = db.query(func.count(Invoice.id)).scalar() or 0
            product_count = db.query(func.count(Product.id)).scalar() or 0
            
            return {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3),
                'database_status': db_status,
                'record_counts': {
                    'customers': customer_count,
                    'suppliers': supplier_count,
                    'invoices': invoice_count,
                    'products': product_count
                }
            }
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                'cpu_percent': 0,
                'memory_percent': 0,
                'memory_used_gb': 0,
                'memory_total_gb': 0,
                'disk_percent': 0,
                'disk_used_gb': 0,
                'disk_total_gb': 0,
                'database_status': 'Error',
                'record_counts': {
                    'customers': 0,
                    'suppliers': 0,
                    'invoices': 0,
                    'products': 0
                }
            }
    
    def get_geographic_distribution(self) -> Dict[str, Any]:
        """Get geographic distribution of customers and suppliers."""
        try:
            db = self.get_session()
            
            # Customer distribution by city
            customer_cities = db.query(
                Customer.city,
                func.count(Customer.id).label('count')
            ).filter(Customer.city.isnot(None)).group_by(Customer.city).all()
            
            # Supplier distribution by city
            supplier_cities = db.query(
                Supplier.city,
                func.count(Supplier.id).label('count')
            ).filter(Supplier.city.isnot(None)).group_by(Supplier.city).all()
            
            return {
                'customer_cities': [
                    {'city': row.city, 'count': row.count}
                    for row in customer_cities
                ],
                'supplier_cities': [
                    {'city': row.city, 'count': row.count}
                    for row in supplier_cities
                ]
            }
        except Exception as e:
            logger.error(f"Error getting geographic distribution: {e}")
            return {'customer_cities': [], 'supplier_cities': []}
    
    def get_user_activity_metrics(self, days: int = 7) -> Dict[str, Any]:
        """Get user activity metrics."""
        try:
            db = self.get_session()
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # Recent activities
            recent_activities = db.query(UserActivity).filter(
                UserActivity.created_at >= start_date
            ).order_by(UserActivity.created_at.desc()).limit(20).all()

            # Activity counts by action
            activity_counts = db.query(
                UserActivity.action,
                func.count(UserActivity.id).label('count')
            ).filter(
                UserActivity.created_at >= start_date
            ).group_by(UserActivity.action).all()

            # Active sessions
            active_sessions = db.query(UserSession).filter(
                UserSession.is_active == 'active'
            ).count()

            # Most active users
            active_users = db.query(
                User.username,
                User.full_name,
                func.count(UserActivity.id).label('activity_count')
            ).join(UserActivity).filter(
                UserActivity.created_at >= start_date
            ).group_by(User.id).order_by(
                desc('activity_count')
            ).limit(10).all()

            return {
                'recent_activities': [
                    {
                        'action': activity.action,
                        'module': activity.module,
                        'description': activity.description,
                        'user_id': activity.user_id,
                        'created_at': activity.created_at.strftime('%Y-%m-%d %H:%M:%S')
                    } for activity in recent_activities
                ],
                'activity_counts': [
                    {
                        'action': row.action,
                        'count': row.count
                    } for row in activity_counts
                ],
                'active_sessions': active_sessions,
                'most_active_users': [
                    {
                        'username': row.username,
                        'full_name': row.full_name,
                        'activity_count': row.activity_count
                    } for row in active_users
                ]
            }
        except Exception as e:
            logger.error(f"Error getting user activity metrics: {e}")
            return {
                'recent_activities': [],
                'activity_counts': [],
                'active_sessions': 0,
                'most_active_users': []
            }

    def get_all_dashboard_data(self) -> Dict[str, Any]:
        """Get all dashboard data in one call."""
        if not self._should_refresh_cache():
            return self._cached_data

        try:
            data = {
                'sales_metrics': self.get_sales_metrics(),
                'invoice_status': self.get_invoice_status_metrics(),
                'top_customers': self.get_top_customers(),
                'top_suppliers': self.get_top_suppliers(),
                'recent_transactions': self.get_recent_transactions(),
                'system_health': self.get_system_health(),
                'geographic_distribution': self.get_geographic_distribution(),
                'user_activity': self.get_user_activity_metrics(),
                'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            self._cached_data = data
            self._last_refresh = datetime.now()
            return data
        except Exception as e:
            logger.error(f"Error getting dashboard data: {e}")
            return self._cached_data if self._cached_data else {}
        finally:
            self.close_session()
