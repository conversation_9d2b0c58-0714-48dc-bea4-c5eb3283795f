import pyodbc
from pathlib import Path
from datetime import datetime

def get_connection(db_path, username='', password=''):
    """Establish a connection to the Access database."""
    conn_strs = [
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};UID={username};PWD={password};',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path};UID={username};PWD={password};',
    ]
    
    for conn_str in conn_strs:
        try:
            conn = pyodbc.connect(conn_str)
            print("Successfully connected to the database.")
            return conn
        except pyodbc.Error as e:
            continue
    
    print("Failed to connect to the database with any connection string.")
    return None

def get_tables(conn):
    """Get list of tables in the database."""
    cursor = conn.cursor()
    tables = []
    for table in cursor.tables(tableType='TABLE'):
        table_name = table.table_name
        if not table_name.startswith('MSys'):
            tables.append(table_name)
    return sorted(tables)

def get_columns(conn, table_name):
    """Get column information for a table."""
    cursor = conn.cursor()
    columns = []
    
    # Get column information
    for col in cursor.columns(table=table_name):
        columns.append({
            'name': col.column_name,
            'type': col.type_name,
            'size': col.column_size,
            'nullable': col.nullable == 1,
            'primary_key': False
        })
    
    # Try to get primary key information
    try:
        pkeys = [row.column_name for row in cursor.primaryKeys(table=table_name)]
        for col in columns:
            if col['name'] in pkeys:
                col['primary_key'] = True
    except:
        pass  # Some drivers don't support primaryKeys()
    
    return columns

def get_sample_data(conn, table_name, limit=3):
    """Get sample data from a table."""
    cursor = conn.cursor()
    try:
        cursor.execute(f'SELECT TOP {limit} * FROM [{table_name}]')
        
        # Get column names
        columns = [column[0] for column in cursor.description]
        
        # Get data rows
        rows = []
        for row in cursor.fetchall():
            rows.append(dict(zip(columns, row)))
        
        return rows
    except Exception as e:
        print(f"  Could not fetch sample data: {e}")
        return []

def analyze_database(db_path, username='', password=''):
    """Analyze the database and return its structure."""
    conn = get_connection(db_path, username, password)
    if not conn:
        return None
    
    try:
        # Get database structure
        tables = get_tables(conn)
        
        db_structure = {
            'database': Path(db_path).name,
            'tables': {},
            'analysis_date': datetime.now().isoformat()
        }
        
        # Analyze each table
        for table_name in tables:
            print(f"Analyzing table: {table_name}")
            
            columns = get_columns(conn, table_name)
            sample_data = get_sample_data(conn, table_name)
            
            db_structure['tables'][table_name] = {
                'columns': columns,
                'sample_data': sample_data,
                'row_count': len(sample_data)  # Sample count, not total
            }
        
        return db_structure
        
    except Exception as e:
        print(f"Error analyzing database: {e}")
        return None
    finally:
        conn.close()

def print_analysis(structure):
    """Print the database analysis to the console."""
    print("\n" + "=" * 80)
    print(f"DATABASE: {structure['database']}")
    print(f"ANALYSIS DATE: {structure['analysis_date']}")
    print("=" * 80 + "\n")
    
    for table_name, table_info in structure['tables'].items():
        print(f"TABLE: {table_name}")
        print("-" * 80)
        
        # Print column information
        print("COLUMNS:")
        for col in table_info['columns']:
            pk = " (PK)" if col['primary_key'] else ""
            nullable = "NULL" if col['nullable'] else "NOT NULL"
            print(f"  {col['name']}: {col['type']}({col['size'] or '?'}) {nullable}{pk}")
        
        # Print sample data
        if table_info['sample_data']:
            print("\nSAMPLE DATA:")
            for row in table_info['sample_data']:
                print("  {" + ", ".join(f"{k}: {v}" for k, v in row.items()) + "}")
        
        print("\n" + "=" * 80 + "\n")

if __name__ == "__main__":
    # Database connection details
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    username = "majid"
    password = "majid"
    
    print(f"Analyzing database: {db_path}")
    
    # Check if database file exists
    if not Path(db_path).exists():
        print(f"Error: Database file not found at {db_path}")
    else:
        # Analyze the database
        structure = analyze_database(db_path, username, password)
        
        if structure:
            # Print the analysis
            print_analysis(structure)
            
            # Print summary
            print("\n=== ANALYSIS COMPLETE ===")
            print(f"Tables analyzed: {len(structure['tables'])}")
            print("=" * 80)
