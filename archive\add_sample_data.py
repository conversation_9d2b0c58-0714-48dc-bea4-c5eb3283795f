"""
<PERSON><PERSON><PERSON> to add sample data to the database for testing purposes.
"""

from sqlalchemy.orm import sessionmaker
from database.session import engine
from models import Customer, Product, ProductCategory  # Import from models package
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

# Create session factory
Session = sessionmaker(bind=engine)

def add_sample_customers():
    """Add sample customers to the database."""
    session = Session()
    
    try:
        # Check if customers already exist
        existing_count = session.query(Customer).count()
        if existing_count > 0:
            print(f"Database already has {existing_count} customers. Skipping sample data creation.")
            return
        
        # Sample customers
        customers = [
            {
                'code': 'CUST001',
                'name': 'ABC Trading Company',
                'contact_person': '<PERSON>',
                'phone': '+971-4-123-4567',
                'mobile': '+971-50-123-4567',
                'email': '<EMAIL>',
                'address': 'Office 123, Business Bay Tower',
                'city': 'Dubai',
                'state': 'Dubai',
                'postal_code': '12345',
                'country': 'UAE',
                'tax_id': 'TRN123456789',
                'credit_limit': 50000.00,
                'current_balance': 15000.00,
                'notes': 'Long-term customer with excellent payment history',
                'is_active': True
            },
            {
                'code': 'CUST002',
                'name': 'Gulf Electronics LLC',
                'contact_person': 'Fatima Al-Zahra',
                'phone': '+971-2-987-6543',
                'mobile': '+971-55-987-6543',
                'email': '<EMAIL>',
                'address': 'Industrial Area 2, Warehouse 45',
                'city': 'Abu Dhabi',
                'state': 'Abu Dhabi',
                'postal_code': '54321',
                'country': 'UAE',
                'tax_id': 'TRN987654321',
                'credit_limit': 75000.00,
                'current_balance': 0.00,
                'notes': 'Electronics retailer, bulk orders',
                'is_active': True
            },
            {
                'code': 'CUST003',
                'name': 'Sharjah Construction Co.',
                'contact_person': 'Mohammed Hassan',
                'phone': '+971-6-555-1234',
                'mobile': '+971-52-555-1234',
                'email': '<EMAIL>',
                'address': 'Industrial Area 1, Plot 67',
                'city': 'Sharjah',
                'state': 'Sharjah',
                'postal_code': '67890',
                'country': 'UAE',
                'tax_id': 'TRN555123456',
                'credit_limit': 100000.00,
                'current_balance': 25000.00,
                'notes': 'Construction materials supplier',
                'is_active': True
            },
            {
                'code': 'CUST004',
                'name': 'Al-Noor Textiles',
                'contact_person': 'Aisha Al-Mansouri',
                'phone': '+971-4-777-8888',
                'mobile': '+971-56-777-8888',
                'email': '<EMAIL>',
                'address': 'Textile Souk, Shop 234',
                'city': 'Dubai',
                'state': 'Dubai',
                'postal_code': '11111',
                'country': 'UAE',
                'tax_id': 'TRN777888999',
                'credit_limit': 30000.00,
                'current_balance': 5000.00,
                'notes': 'Textile and fabric supplier',
                'is_active': True
            },
            {
                'code': 'CUST005',
                'name': 'Ras Al Khaimah Motors',
                'contact_person': 'Omar Al-Qasimi',
                'phone': '+971-7-222-3333',
                'mobile': '+971-50-222-3333',
                'email': '<EMAIL>',
                'address': 'Sheikh Mohammed Bin Rashid Road',
                'city': 'Ras Al Khaimah',
                'state': 'Ras Al Khaimah',
                'postal_code': '22222',
                'country': 'UAE',
                'tax_id': 'TRN222333444',
                'credit_limit': 80000.00,
                'current_balance': 0.00,
                'notes': 'Automotive parts and accessories',
                'is_active': True
            },
            {
                'code': 'CUST006',
                'name': 'Fujairah Food Supplies',
                'contact_person': 'Mariam Al-Sharqi',
                'phone': '+971-9-444-5555',
                'mobile': '+971-55-444-5555',
                'email': '<EMAIL>',
                'address': 'Port Area, Warehouse Complex B',
                'city': 'Fujairah',
                'state': 'Fujairah',
                'postal_code': '33333',
                'country': 'UAE',
                'tax_id': 'TRN444555666',
                'credit_limit': 40000.00,
                'current_balance': 8000.00,
                'notes': 'Food and beverage distributor',
                'is_active': True
            },
            {
                'code': 'CUST007',
                'name': 'Ajman Steel Works',
                'contact_person': 'Khalid Al-Nuaimi',
                'phone': '+971-6-666-7777',
                'mobile': '+971-52-666-7777',
                'email': '<EMAIL>',
                'address': 'Heavy Industrial Area, Plot 89',
                'city': 'Ajman',
                'state': 'Ajman',
                'postal_code': '44444',
                'country': 'UAE',
                'tax_id': 'TRN666777888',
                'credit_limit': 120000.00,
                'current_balance': 35000.00,
                'notes': 'Steel fabrication and construction materials',
                'is_active': True
            },
            {
                'code': 'CUST008',
                'name': 'Umm Al Quwain Trading',
                'contact_person': 'Nadia Al-Mualla',
                'phone': '+971-6-888-9999',
                'mobile': '+971-56-888-9999',
                'email': '<EMAIL>',
                'address': 'Free Zone Area, Office 456',
                'city': 'Umm Al Quwain',
                'state': 'Umm Al Quwain',
                'postal_code': '55555',
                'country': 'UAE',
                'tax_id': 'TRN888999000',
                'credit_limit': 25000.00,
                'current_balance': 0.00,
                'notes': 'General trading company',
                'is_active': True
            },
            {
                'code': 'CUST009',
                'name': 'Dubai Tech Solutions',
                'contact_person': 'Hassan Al-Maktoum',
                'phone': '+971-4-111-2222',
                'mobile': '+971-50-111-2222',
                'email': '<EMAIL>',
                'address': 'Dubai Internet City, Building 7',
                'city': 'Dubai',
                'state': 'Dubai',
                'postal_code': '66666',
                'country': 'UAE',
                'tax_id': 'TRN111222333',
                'credit_limit': 60000.00,
                'current_balance': 12000.00,
                'notes': 'IT services and software solutions',
                'is_active': True
            },
            {
                'code': 'CUST010',
                'name': 'Inactive Customer Ltd.',
                'contact_person': 'Test Contact',
                'phone': '+971-4-000-0000',
                'mobile': '+971-50-000-0000',
                'email': '<EMAIL>',
                'address': 'Test Address',
                'city': 'Dubai',
                'state': 'Dubai',
                'postal_code': '00000',
                'country': 'UAE',
                'tax_id': 'TRN000000000',
                'credit_limit': 10000.00,
                'current_balance': 0.00,
                'notes': 'This is an inactive customer for testing',
                'is_active': False
            }
        ]
        
        # Add customers to database
        for customer_data in customers:
            customer = Customer(**customer_data)
            session.add(customer)
        
        session.commit()
        print(f"Successfully added {len(customers)} sample customers to the database.")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Error adding sample customers: {e}")
        print(f"Error adding sample customers: {e}")
    finally:
        session.close()

def main():
    """Main function to add sample data."""
    print("Adding sample data to the database...")
    add_sample_customers()
    print("Sample data creation completed.")

if __name__ == "__main__":
    main()
