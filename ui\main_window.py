import logging
from PyQt6.QtWidgets import (Q<PERSON>ainWindow, QA<PERSON>lication, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QTabWidget, QLabel, QStatusBar, QToolBar, QMessageBox,
                             QDockWidget, QListWidget, QStackedWidget, QMenuBar, QMenu, QFileDialog,
                             QSplitter, QLineEdit, QTextEdit, QDateEdit, QTableWidget, QTableWidgetItem, QComboBox)

# Set up logger
logger = logging.getLogger(__name__)
from PyQt6.QtCore import Qt, QSize, QPropertyAnimation, QEasingCurve, QPoint, QDate
from PyQt6.QtGui import QIcon, QAction, QPixmap, QFontDatabase, QFont, QPainter
from PyQt6.QtSvg import QSvgRenderer
import sys
import os
from pathlib import Path

from config import settings
from database.session import init_db
from modules.customer_management import CustomerManagementWidget
from modules.supplier_management import SupplierManagementWidget
from ui.dark_theme import get_dark_theme_stylesheet
from ui.invoice_management_widget import InvoiceDashboardWidget
from ui.dashboard_widget import DashboardWidget

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"{settings.APP_NAME} - {settings.VERSION}")
        self.setMinimumSize(settings.UI_SETTINGS['min_width'], settings.UI_SETTINGS['min_height'])
        
        # Initialize database
        if not init_db():
            QMessageBox.critical(self, "Database Error", "Failed to connect to the database.")
            sys.exit(1)
        
        # Set up the main window
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the main user interface."""
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)  # Remove margins for a more modern look
        
        # Define sidebar width constants
        self.sidebar_expanded_width = 200
        self.sidebar_collapsed_width = 60
        
        # Create main content area with splitter for better resizing
        self.content_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.content_splitter.setChildrenCollapsible(False)  # Prevent collapsing children to zero size

        # Create navigation panel (left sidebar)
        self.nav_panel_container = QWidget()
        self.nav_panel_container.setObjectName("navPanel")  # For styling
        self.nav_panel_container.setMinimumWidth(self.sidebar_expanded_width)
        self.nav_panel_container.setMaximumWidth(self.sidebar_expanded_width)  # Also set maximum width
        self.nav_panel_layout = QVBoxLayout(self.nav_panel_container)
        self.nav_panel_layout.setContentsMargins(0, 0, 0, 0)
        self.nav_panel_layout.setSpacing(0)

        # Create header for navigation panel
        nav_header = QWidget()
        nav_header.setObjectName("navHeader")
        nav_header.setFixedHeight(50)
        nav_header_layout = QHBoxLayout(nav_header)
        nav_header_layout.setContentsMargins(15, 0, 15, 0)

        # Create toggle button
        self.toggle_button = QPushButton()
        self.toggle_button.setObjectName("toggleButton")
        self.toggle_button.setFixedSize(QSize(30, 30))
        self.toggle_button.setIcon(QIcon(str(Path("ui/resources/menu.png"))))  # Add your menu icon
        self.toggle_button.clicked.connect(self.toggle_navigation_panel)
        nav_header_layout.addWidget(self.toggle_button)
        
        # Add stretch to fill space
        nav_header_layout.addStretch()

        self.nav_panel_layout.addWidget(nav_header)

        # Add navigation buttons
        self.nav_buttons_widget = self.create_navigation_panel_buttons()
        self.nav_panel_layout.addWidget(self.nav_buttons_widget)
        
        # Add nav panel to splitter
        self.content_splitter.addWidget(self.nav_panel_container)

        # Create main content area (right side)
        self.main_content = QStackedWidget()
        self.main_content.setObjectName("mainContent")
        self.content_splitter.addWidget(self.main_content)
        
        # Set initial sizes for splitter
        self.content_splitter.setSizes([150, self.width() - 150])
        
        # Add splitter to main layout
        main_layout.addWidget(self.content_splitter)

        # Create animations for both minimum and maximum width
        self.sidebar_min_animation = QPropertyAnimation(self.nav_panel_container, b"minimumWidth")
        self.sidebar_min_animation.setDuration(200)
        self.sidebar_min_animation.setEasingCurve(QEasingCurve.Type.OutQuint)
        
        self.sidebar_max_animation = QPropertyAnimation(self.nav_panel_container, b"maximumWidth")
        self.sidebar_max_animation.setDuration(200)
        self.sidebar_max_animation.setEasingCurve(QEasingCurve.Type.OutQuint)
        
        # Connect finished signal to update the splitter
        self.sidebar_min_animation.finished.connect(self.update_splitter)

        # Create status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")

        # Apply styles
        self.apply_styles()

        # Show default module (Dashboard)
        self.show_module("Dashboard")
        
        # Initialize sidebar in expanded state
        self.is_sidebar_expanded = True
    
    def create_toolbar(self):
        """Create the main toolbar."""
        toolbar = QToolBar("Main Toolbar")
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)
        
        # Add actions to toolbar
        new_action = QAction("New", self)
        new_action.setStatusTip("Create a new document")
        toolbar.addAction(new_action)
        
        save_action = QAction("Save", self)
        save_action.setStatusTip("Save the current document")
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        cut_action = QAction("Cut", self)
        cut_action.setStatusTip("Cut selection to clipboard")
        toolbar.addAction(cut_action)
        
        copy_action = QAction("Copy", self)
        copy_action.setStatusTip("Copy selection to clipboard")
        toolbar.addAction(copy_action)
        
        paste_action = QAction("Paste", self)
        paste_action.setStatusTip("Paste from clipboard")
        toolbar.addAction(paste_action)
    
    def create_navigation_panel_buttons(self):
        """Create the widget containing navigation buttons."""
        buttons_widget = QWidget()
        buttons_widget.setObjectName("navButtonsContainer")
        self.nav_buttons_layout = QVBoxLayout(buttons_widget)
        self.nav_buttons_layout.setContentsMargins(8, 8, 8, 8)
        self.nav_buttons_layout.setSpacing(2)

        self.nav_buttons = {}

        # Define modules with their icons
        modules = [
            ("Dashboard", "dashboard.png"),
            ("Suppliers", "supplier.svg"),
            ("Customers", "customers.png"),
            ("Suppliers Invoice", "sales.png"),
            ("Customers Invoice", "sales.png"),
            ("Accounting", "accounting.svg"),
            ("Reports", "reports.png"),
            ("Settings", "settings.svg")
        ]

        for module_name, icon_file in modules:
            # Create the main button
            btn = QPushButton()
            btn.setObjectName("navButton")
            btn.setCheckable(True)
            btn.setFixedHeight(40)
            
            # Create a container widget for the button content
            container = QWidget()
            btn_layout = QHBoxLayout(container)
            btn_layout.setContentsMargins(12, 0, 12, 0)
            btn_layout.setSpacing(12)
            
            # Add icon
            icon_label = QLabel()
            icon_label.setObjectName("navButtonIcon")
            icon_label.setFixedSize(20, 20)
            
            try:
                # Get the absolute path to the resources directory
                base_dir = Path(__file__).parent  # This is the ui/ directory
                icon_path = base_dir / "resources" / icon_file
                
                logger.debug(f"Looking for icon at: {icon_path}")
                
                if icon_path.exists():
                    try:
                        # Use QIcon which can handle both SVG and PNG
                        icon = QIcon(str(icon_path))
                        if not icon.isNull():
                            # Convert icon to pixmap for the label
                            pixmap = icon.pixmap(QSize(20, 20))
                            icon_label.setPixmap(pixmap)
                            logger.debug(f"Successfully loaded icon: {icon_file}")
                        else:
                            logger.warning(f"Failed to load icon: {icon_path}")
                            icon_label.setText("•")  # Use a bullet point as a fallback
                    except Exception as icon_error:
                        logger.error(f"Error loading icon {icon_file}: {icon_error}")
                        icon_label.setText("•")  # Use a bullet point as a fallback
                else:
                    logger.warning(f"Icon file not found: {icon_path}")
                    icon_label.setText("•")  # Use a bullet point as a fallback
            except Exception as e:
                logger.error(f"Unexpected error loading icon {icon_file}", exc_info=True)
                icon_label.setText("•")  # Use a bullet point as a fallback
            
            btn_layout.addWidget(icon_label)
            
            # Add text label
            text_label = QLabel(module_name)
            text_label.setObjectName("navButtonText")
            text_label.setFixedHeight(20)
            btn_layout.addWidget(text_label)
            btn_layout.addStretch()
            
            # Set the container as the button's layout
            btn_container_layout = QHBoxLayout(btn)
            btn_container_layout.setContentsMargins(0, 0, 0, 0)
            btn_container_layout.addWidget(container)
            
            # Store references to labels for toggle_navigation_panel
            btn.icon_label = icon_label
            btn.text_label = text_label
            btn.container = container
            
            btn.clicked.connect(lambda checked, m=module_name: self.show_module(m))
            self.nav_buttons_layout.addWidget(btn)
            self.nav_buttons[module_name] = btn

        self.nav_buttons_layout.addStretch()
        return buttons_widget

    def toggle_navigation_panel(self):
        """Toggle the visibility and size of the navigation panel."""
        # Stop any running animation
        self.sidebar_min_animation.stop()
        self.sidebar_max_animation.stop()
        
        if self.is_sidebar_expanded:
            # Collapse the sidebar
            self.sidebar_min_animation.setStartValue(self.nav_panel_container.minimumWidth())
            self.sidebar_min_animation.setEndValue(self.sidebar_collapsed_width)
            self.sidebar_max_animation.setStartValue(self.nav_panel_container.maximumWidth())
            self.sidebar_max_animation.setEndValue(self.sidebar_collapsed_width)
            
            # Update button layouts
            for module_name, button in self.nav_buttons.items():
                button.text_label.hide()
                button.setFixedWidth(self.sidebar_collapsed_width - 16)  # Account for margins
                
            # Update toggle button
            self.toggle_button.setIcon(QIcon(str(Path("ui/resources/menu-right.png"))))
            self.is_sidebar_expanded = False
            
        else:
            # Expand the sidebar
            self.sidebar_min_animation.setStartValue(self.nav_panel_container.minimumWidth())
            self.sidebar_min_animation.setEndValue(self.sidebar_expanded_width)
            self.sidebar_max_animation.setStartValue(self.nav_panel_container.maximumWidth())
            self.sidebar_max_animation.setEndValue(self.sidebar_expanded_width)
            
            # Update button layouts
            for module_name, button in self.nav_buttons.items():
                button.text_label.show()
                button.setFixedWidth(self.sidebar_expanded_width - 16)  # Account for margins
                
            # Update toggle button
            self.toggle_button.setIcon(QIcon(str(Path("ui/resources/menu-left.png"))))
            self.is_sidebar_expanded = True

        # Start the animation
        self.sidebar_min_animation.start()
        self.sidebar_max_animation.start()
    
    def update_splitter(self):
        """Update splitter sizes after sidebar animation completes."""
        # Calculate the space for the content widget
        content_width = self.width() - self.nav_panel_container.width()
        
        # Update the splitter sizes
        self.content_splitter.setSizes([self.nav_panel_container.width(), content_width])
        
        # Force layout update
        self.centralWidget().layout().update()
        self.centralWidget().layout().activate()

    def show_module(self, module_name):
        """Show the selected module in the main content area."""
        # Clear current content
        while self.main_content.count() > 0:
            widget = self.main_content.widget(0)
            self.main_content.removeWidget(widget)
            widget.setParent(None)

        # Create and add the appropriate module widget
        if module_name == "Dashboard":
            # Dashboard Module
            widget = DashboardWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Suppliers":
            # Supplier Management Module
            widget = SupplierManagementWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Customers":
            # Customer Management Module
            widget = CustomerManagementWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Suppliers Invoice":
            # Use the invoice section widget in supplier mode
            widget = InvoiceDashboardWidget(supplier_mode=True)
            self.main_content.addWidget(widget)

        elif module_name == "Customers Invoice":
            # TODO: Implement customers invoice module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Customers Invoice Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Accounting":
            # TODO: Implement accounting module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Accounting Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Reports":
            # TODO: Implement reports module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Reports Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Settings":
            # TODO: Implement settings module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Settings Module - Coming Soon"))
            self.main_content.addWidget(widget)

        else:
            # Default case - show placeholder
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel(f"{module_name} Module - Coming Soon"))
            self.main_content.addWidget(widget)

        # Update navigation button states
        for module, button in self.nav_buttons.items():
            button.setChecked(module == module_name)

        self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")
    
    def new_document(self):
        """Handle new document action."""
        # TODO: Implement new document
        self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")
    
    def open_document(self):
        """Handle open document action."""
        file_name, _ = QFileDialog.getOpenFileName(
            self, "Open Document", "", "All Files (*);;Database Files (*.mdb *.accdb)")
        if file_name:
            # TODO: Handle file opening
            self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")
    
    def save_document(self):
        """Handle save document action."""
        # TODO: Implement save functionality
        self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")
    
    def show_about(self):
        """Show about dialog."""
        QMessageBox.about(self, "About", "Green Nest Trade Co Accounting Application\n\n© 2025 Green Nest Trade Co. All rights reserved.")
    
    def apply_styles(self):
        """Apply styles to the application."""
        # Get the dark theme stylesheet
        stylesheet = get_dark_theme_stylesheet()
        
        # Add custom styles for the navigation panel
        custom_styles = """
            QWidget#navPanel {
                background-color: #2C2C2C;
                border: none;
            }
            
            QWidget#navHeader {
                background-color: #252525;
                border-bottom: 1px solid #3A3A3A;
            }
            
            QPushButton#toggleButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                padding: 4px;
            }
            
            QPushButton#toggleButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            
            QLabel#headerLabel {
                color: #FFFFFF;
                font-size: 16px;
                font-weight: bold;
            }
            
            QWidget#navButtonsContainer {
                background-color: transparent;
            }
            
            QPushButton#navButton {
                background-color: transparent;
                border: none;
                border-radius: 6px;
                text-align: left;
                padding: 8px;
                margin: 2px 4px;
            }
            
            QPushButton#navButton:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            
            QPushButton#navButton:checked {
                background-color: #3A3A3A;
            }
            
            QLabel#navButtonText {
                color: #FFFFFF;
                font-size: 14px;
            }
            
            QStackedWidget#mainContent {
                background-color: #1E1E1E;
            }
        """
        
        # Combine stylesheets
        self.setStyleSheet(stylesheet + custom_styles)
    
    def closeEvent(self, event):
        """Handle window close event."""
        reply = QMessageBox.question(
            self, 'Exit',
            'Are you sure you want to exit?',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            # Clean up resources
            # Note: SQLite connections are automatically closed
            event.accept()
        else:
            event.ignore()

    def resizeEvent(self, event):
        """Handle window resize events."""
        super().resizeEvent(event)
        
        # Update splitter sizes when window is resized
        if hasattr(self, 'content_splitter') and hasattr(self, 'nav_panel_container'):
            nav_width = self.nav_panel_container.width()
            content_width = self.width() - nav_width
            self.content_splitter.setSizes([nav_width, content_width])

class InvoiceWidget(QWidget):
    """ARCHIVED: Old invoice widget, kept for reference."""
    def __init__(self):
        super().__init__()
        self.setObjectName("ArchivedInvoiceWidget")
        self.setup_ui = lambda: None  # Disable setup_ui

    def show_module(self, module_name):
        """Show the selected module in the main content area."""
        # Clear current content
        while self.main_content.count() > 0:
            widget = self.main_content.widget(0)
            self.main_content.removeWidget(widget)
            widget.setParent(None)

        # Create and add the appropriate module widget
        if module_name == "Dashboard":
            # TODO: Implement dashboard
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Dashboard - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Suppliers":
            # Supplier Management Module
            widget = SupplierManagementWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Customers":
            # Customer Management Module
            widget = CustomerManagementWidget()
            self.main_content.addWidget(widget)

        elif module_name == "Suppliers Invoice":
            # TODO: Implement suppliers invoice module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Suppliers Invoice Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Customers Invoice":
            # TODO: Implement customers invoice module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Customers Invoice Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Accounting":
            # TODO: Implement accounting module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Accounting Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Reports":
            # TODO: Implement reports module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Reports Module - Coming Soon"))
            self.main_content.addWidget(widget)

        elif module_name == "Settings":
            # TODO: Implement settings module
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel("Settings Module - Coming Soon"))
            self.main_content.addWidget(widget)

        else:
            # Default case - show placeholder
            widget = QWidget()
            layout = QVBoxLayout(widget)
            layout.addWidget(QLabel(f"{module_name} Module - Coming Soon"))
            self.main_content.addWidget(widget)

        # Update navigation button states
        for module, button in self.nav_buttons.items():
            button.setChecked(module == module_name)

        self.status_bar.showMessage("LoGiT3X was here🤖 Built with late nights ☕️🚬 & bad decisions 😤 If it breaks, it’s a feature🤯")

def main():
    """Main entry point for the application."""
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle(settings.UI_SETTINGS['theme'])
    
    # Create and show the main window
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
