"""
Dark theme stylesheet for the application.

This module provides a comprehensive dark theme with optimized colors
for text, buttons, forms, tables, and all UI components.
"""

# Dark theme color palette
DARK_COLORS = {
    # Background colors
    'bg_primary': '#2b2b2b',        # Main background
    'bg_secondary': '#3c3c3c',      # Secondary background (panels, cards)
    'bg_tertiary': '#4a4a4a',       # Tertiary background (hover states)
    'bg_input': '#404040',          # Input field backgrounds
    'bg_button': '#505050',         # Button backgrounds
    'bg_hover': '#5a5a5a',          # Hover states
    'bg_pressed': '#606060',        # Pressed/active states
    'bg_selected': '#0078d4',       # Selection background
    'bg_disabled': '#333333',       # Disabled elements
    
    # Text colors
    'text_primary': '#ffffff',      # Primary text (high contrast)
    'text_secondary': '#cccccc',    # Secondary text (medium contrast)
    'text_tertiary': '#999999',     # Tertiary text (low contrast)
    'text_disabled': '#666666',     # Disabled text
    'text_link': '#4fc3f7',         # Links
    'text_success': '#4caf50',      # Success messages
    'text_warning': '#ff9800',      # Warning messages
    'text_error': '#f44336',        # Error messages
    
    # Border colors
    'border_primary': '#555555',    # Primary borders
    'border_secondary': '#666666',  # Secondary borders
    'border_focus': '#0078d4',      # Focus borders
    'border_error': '#f44336',      # Error borders
    'border_success': '#4caf50',    # Success borders
    
    # Accent colors
    'accent_blue': '#0078d4',       # Primary accent
    'accent_green': '#4caf50',      # Success accent
    'accent_orange': '#ff9800',     # Warning accent
    'accent_red': '#f44336',        # Error accent
    'accent_purple': '#9c27b0',     # Info accent
}

def get_dark_theme_stylesheet():
    """Get the complete dark theme stylesheet."""
    return f"""
    /* ===== MAIN WINDOW AND GENERAL STYLES ===== */
    QMainWindow {{
        background-color: {DARK_COLORS['bg_primary']};
        color: {DARK_COLORS['text_primary']};
    }}
    
    QWidget {{
        background-color: {DARK_COLORS['bg_primary']};
        color: {DARK_COLORS['text_primary']};
        selection-background-color: {DARK_COLORS['bg_selected']};
        selection-color: {DARK_COLORS['text_primary']};
    }}
    
    /* ===== NAVIGATION PANEL ===== */
    QWidget#navPanel {{
        background-color: {DARK_COLORS['bg_secondary']};
        border-right: 1px solid {DARK_COLORS['border_primary']};
    }}

    QWidget#navHeader {{
        background-color: {DARK_COLORS['bg_secondary']};
        border-bottom: 1px solid {DARK_COLORS['border_primary']};
    }}

    QWidget#navButtonsContainer {{
        background-color: {DARK_COLORS['bg_secondary']};
    }}

    QPushButton#navButton {{
        background-color: transparent;
        border: none;
        border-radius: 4px;
        text-align: left;
        padding: 10px;
        margin: 2px 8px;
    }}

    QPushButton#navButton:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}

    QPushButton#navButton:pressed {{
        background-color: {DARK_COLORS['bg_pressed']};
    }}

    QPushButton#navButton:checked {{
        background-color: {DARK_COLORS['accent_blue']};
    }}

    QLabel#navButtonText {{
        color: {DARK_COLORS['text_primary']};
        background-color: transparent;
        font-size: 15px;
    }}

    /* Style for icon labels in nav buttons */
    QLabel#navButtonIcon {{
        background-color: transparent;
    }}
    
    /* ===== BUTTONS ===== */
    QPushButton {{
        background-color: {DARK_COLORS['bg_button']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 4px;
        padding: 10px 18px;
        margin: 2px;
        font-weight: 500;
        min-height: 20px;
    }}

    /* Specific style for the sidebar toggle button */
    QPushButton#toggleButton {{ /* Assuming objectName is set to 'toggleButton' */
        background-color: {DARK_COLORS['bg_secondary']};
        border: none;
        border-radius: 0px; /* No border radius for a sharp edge */
        padding: 0px;
        margin: 0px;
        font-size: 20px;
        font-weight: bold;
        color: {DARK_COLORS['text_primary']};
        min-width: 40px;
        max-width: 40px;
        min-height: 40px;
        max-height: 40px;
    }}

    QPushButton#toggleButton:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}

    QPushButton#toggleButton:pressed {{
        background-color: {DARK_COLORS['bg_pressed']};
    }}
    
    QPushButton:hover {{
        background-color: {DARK_COLORS['bg_hover']};
        border-color: {DARK_COLORS['border_secondary']};
    }}
    
    QPushButton:pressed {{
        background-color: {DARK_COLORS['bg_pressed']};
        border-color: {DARK_COLORS['border_focus']};
    }}
    
    QPushButton:checked {{
        background-color: {DARK_COLORS['accent_blue']};
        color: {DARK_COLORS['text_primary']};
        border-color: {DARK_COLORS['accent_blue']};
        font-weight: bold;
    }}
    
    QPushButton:disabled {{
        background-color: {DARK_COLORS['bg_disabled']};
        color: {DARK_COLORS['text_disabled']};
        border-color: {DARK_COLORS['text_disabled']};
    }}
    
    /* ===== INPUT FIELDS ===== */
    QLineEdit, QTextEdit, QPlainTextEdit {{
        background-color: {DARK_COLORS['bg_input']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 4px;
        padding: 8px 10px;
        selection-background-color: {DARK_COLORS['bg_selected']};
    }}
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
        border-color: {DARK_COLORS['border_focus']};
        background-color: {DARK_COLORS['bg_secondary']};
    }}
    
    QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled {{
        background-color: {DARK_COLORS['bg_disabled']};
        color: {DARK_COLORS['text_disabled']};
        border-color: {DARK_COLORS['text_disabled']};
    }}
    
    /* ===== SPIN BOXES ===== */
    QSpinBox, QDoubleSpinBox {{
        background-color: {DARK_COLORS['bg_input']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 4px;
        padding: 8px 10px;
        selection-background-color: {DARK_COLORS['bg_selected']};
    }}
    
    QSpinBox:focus, QDoubleSpinBox:focus {{
        border-color: {DARK_COLORS['border_focus']};
        background-color: {DARK_COLORS['bg_secondary']};
    }}
    
    QSpinBox::up-button, QDoubleSpinBox::up-button {{
        background-color: {DARK_COLORS['bg_button']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 2px;
        width: 16px;
    }}
    
    QSpinBox::down-button, QDoubleSpinBox::down-button {{
        background-color: {DARK_COLORS['bg_button']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 2px;
        width: 16px;
    }}
    
    QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover,
    QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    /* ===== COMBO BOXES ===== */
    QComboBox {{
        background-color: {DARK_COLORS['bg_input']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 4px;
        padding: 8px 10px;
        min-width: 100px;
    }}
    
    QComboBox:focus {{
        border-color: {DARK_COLORS['border_focus']};
        background-color: {DARK_COLORS['bg_secondary']};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 20px;
    }}
    
    QComboBox::down-arrow {{
        image: none;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid {DARK_COLORS['text_secondary']};
        margin-right: 5px;
    }}
    
    QComboBox QAbstractItemView {{
        background-color: {DARK_COLORS['bg_secondary']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        selection-background-color: {DARK_COLORS['bg_selected']};
        outline: none;
    }}
    
    /* ===== LABELS ===== */
    QLabel {{
        color: {DARK_COLORS['text_primary']};
        background-color: transparent;
    }}
    
    QLabel[class="secondary"] {{
        color: {DARK_COLORS['text_secondary']};
    }}
    
    QLabel[class="tertiary"] {{
        color: {DARK_COLORS['text_tertiary']};
    }}
    
    /* ===== GROUP BOXES ===== */
    QGroupBox {{
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 6px;
        margin-top: 10px;
        padding-top: 10px;
        font-weight: bold;
    }}
    
    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 8px 0 8px;
        background-color: {DARK_COLORS['bg_primary']};
        color: {DARK_COLORS['text_primary']};
    }}
    
    /* ===== TABLES ===== */
    QTableWidget {{
        background-color: {DARK_COLORS['bg_secondary']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        gridline-color: {DARK_COLORS['border_primary']};
        selection-background-color: {DARK_COLORS['bg_selected']};
        alternate-background-color: {DARK_COLORS['bg_tertiary']};
    }}
    
    QTableWidget::item {{
        padding: 10px;
        border: none;
    }}
    
    QTableWidget::item:selected {{
        background-color: {DARK_COLORS['bg_selected']};
        color: {DARK_COLORS['text_primary']};
    }}
    
    QTableWidget::item:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    QHeaderView::section {{
        background-color: {DARK_COLORS['bg_tertiary']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        padding: 10px;
        font-weight: bold;
    }}
    
    QHeaderView::section:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    /* ===== SCROLL BARS ===== */
    QScrollBar:vertical {{
        background-color: {DARK_COLORS['bg_secondary']};
        width: 12px;
        border: none;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {DARK_COLORS['bg_button']};
        border-radius: 6px;
        min-height: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        border: none;
        background: none;
    }}
    
    QScrollBar:horizontal {{
        background-color: {DARK_COLORS['bg_secondary']};
        height: 12px;
        border: none;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:horizontal {{
        background-color: {DARK_COLORS['bg_button']};
        border-radius: 6px;
        min-width: 20px;
        margin: 2px;
    }}
    
    QScrollBar::handle:horizontal:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
        border: none;
        background: none;
    }}
    
    /* ===== STATUS BAR ===== */
    QStatusBar {{
        background-color: {DARK_COLORS['bg_tertiary']};
        color: {DARK_COLORS['text_primary']};
        border-top: 1px solid {DARK_COLORS['border_primary']};
        padding: 4px;
    }}
    
    /* ===== MENU BAR ===== */
    QMenuBar {{
        background-color: {DARK_COLORS['bg_secondary']};
        color: {DARK_COLORS['text_primary']};
        border-bottom: 1px solid {DARK_COLORS['border_primary']};
    }}
    
    QMenuBar::item {{
        background-color: transparent;
        padding: 6px 12px;
    }}
    
    QMenuBar::item:selected {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    QMenu {{
        background-color: {DARK_COLORS['bg_secondary']};
        color: {DARK_COLORS['text_primary']};
        border: 1px solid {DARK_COLORS['border_primary']};
    }}
    
    QMenu::item {{
        padding: 8px 22px;
    }}
    
    QMenu::item:selected {{
        background-color: {DARK_COLORS['bg_selected']};
    }}
    
    /* ===== DIALOGS ===== */
    QDialog {{
        background-color: {DARK_COLORS['bg_primary']};
        color: {DARK_COLORS['text_primary']};
    }}
    
    QDialogButtonBox {{
        background-color: transparent;
    }}
    
    QDialogButtonBox QPushButton {{
        min-width: 80px;
    }}
    
    /* ===== MESSAGE BOXES ===== */
    QMessageBox {{
        background-color: {DARK_COLORS['bg_primary']};
        color: {DARK_COLORS['text_primary']};
    }}
    
    QMessageBox QPushButton {{
        min-width: 80px;
        padding: 8px 18px;
    }}
    
    /* ===== TAB WIDGET ===== */
    QTabWidget::pane {{
        border: 1px solid {DARK_COLORS['border_primary']};
        background-color: {DARK_COLORS['bg_secondary']};
    }}
    
    QTabBar::tab {{
        background-color: {DARK_COLORS['bg_tertiary']};
        color: {DARK_COLORS['text_secondary']};
        border: 1px solid {DARK_COLORS['border_primary']};
        padding: 10px 18px;
        margin-right: 2px;
    }}
    
    QTabBar::tab:selected {{
        background-color: {DARK_COLORS['bg_secondary']};
        color: {DARK_COLORS['text_primary']};
        border-bottom-color: {DARK_COLORS['bg_secondary']};
    }}
    
    QTabBar::tab:hover {{
        background-color: {DARK_COLORS['bg_hover']};
    }}
    
    /* ===== PROGRESS BAR ===== */
    QProgressBar {{
        background-color: {DARK_COLORS['bg_input']};
        border: 1px solid {DARK_COLORS['border_primary']};
        border-radius: 4px;
        text-align: center;
        color: {DARK_COLORS['text_primary']};
    }}
    
    QProgressBar::chunk {{
        background-color: {DARK_COLORS['accent_blue']};
        border-radius: 3px;
    }}
    
    /* ===== CHECKBOXES AND RADIO BUTTONS ===== */
    QCheckBox, QRadioButton {{
        color: {DARK_COLORS['text_primary']};
        spacing: 8px;
    }}
    
    QCheckBox::indicator, QRadioButton::indicator {{
        width: 16px;
        height: 16px;
        background-color: {DARK_COLORS['bg_input']};
        border: 1px solid {DARK_COLORS['border_primary']};
    }}
    
    QCheckBox::indicator {{
        border-radius: 3px;
    }}
    
    QRadioButton::indicator {{
        border-radius: 8px;
    }}
    
    QCheckBox::indicator:checked {{
        background-color: {DARK_COLORS['accent_blue']};
        border-color: {DARK_COLORS['accent_blue']};
    }}
    
    QRadioButton::indicator:checked {{
        background-color: {DARK_COLORS['accent_blue']};
        border-color: {DARK_COLORS['accent_blue']};
    }}
    
    QCheckBox::indicator:hover, QRadioButton::indicator:hover {{
        border-color: {DARK_COLORS['border_focus']};
    }}
    """

def get_success_style():
    """Get success styling for form validation."""
    return f"border: 2px solid {DARK_COLORS['border_success']}; background-color: {DARK_COLORS['bg_input']};"

def get_error_style():
    """Get error styling for form validation."""
    return f"border: 2px solid {DARK_COLORS['border_error']}; background-color: {DARK_COLORS['bg_input']};"

def get_normal_style():
    """Get normal styling for form fields."""
    return f"border: 1px solid {DARK_COLORS['border_primary']}; background-color: {DARK_COLORS['bg_input']};"
