2025-07-31 14:06:07,764 - __main__ - INFO - Logging initialized
2025-07-31 14:06:07,765 - __main__ - INFO - Starting application initialization
2025-07-31 14:06:07,806 - config.settings - INFO - Application starting in development mode
2025-07-31 14:06:07,807 - config.settings - INFO - Database: sqlite:///./data/green_nest_trade_co.db
2025-07-31 14:06:08,204 - matplotlib - DEBUG - matplotlib data path: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\matplotlib\mpl-data
2025-07-31 14:06:08,212 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-31 14:06:08,213 - matplotlib - DEBUG - interactive is False
2025-07-31 14:06:08,213 - matplotlib - DEBUG - platform is win32
2025-07-31 14:06:08,260 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-31 14:06:08,264 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-31 14:06:09,246 - __main__ - INFO - Initializing database...
2025-07-31 14:06:09,247 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 14:06:09,247 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-31 14:06:09,247 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,248 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-31 14:06:09,248 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,249 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-31 14:06:09,249 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,250 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-31 14:06:09,250 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,250 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-31 14:06:09,251 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,251 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-31 14:06:09,251 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("suppliers")
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_activities")
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_sessions")
2025-07-31 14:06:09,252 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:06:09,253 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 14:06:09,253 - __main__ - INFO - Database initialized successfully
2025-07-31 14:06:09,253 - __main__ - INFO - Creating application instance
2025-07-31 14:06:09,268 - __main__ - INFO - Creating login window
2025-07-31 14:06:09,286 - passlib.utils.compat - DEBUG - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-31 14:06:09,287 - passlib.utils.compat - DEBUG - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-31 14:06:09,287 - passlib.utils.compat - DEBUG - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-31 14:06:09,391 - passlib.registry - DEBUG - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-31 14:06:09,461 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-31 14:06:09,463 - passlib.handlers.bcrypt - DEBUG - detected 'bcrypt' backend, version '<unknown>'
2025-07-31 14:06:09,463 - passlib.handlers.bcrypt - DEBUG - 'bcrypt' backend lacks $2$ support, enabling workaround
2025-07-31 14:06:09,669 - __main__ - INFO - Login window created successfully
2025-07-31 14:06:09,724 - __main__ - INFO - Login window shown
2025-07-31 14:06:09,725 - __main__ - INFO - Starting application event loop
