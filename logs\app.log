2025-07-31 14:17:51,004 - __main__ - INFO - Logging initialized
2025-07-31 14:17:51,005 - __main__ - INFO - Starting application initialization
2025-07-31 14:17:51,042 - config.settings - INFO - Application starting in development mode
2025-07-31 14:17:51,043 - config.settings - INFO - Database: sqlite:///./data/green_nest_trade_co.db
2025-07-31 14:17:51,454 - matplotlib - DEBUG - matplotlib data path: C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\matplotlib\mpl-data
2025-07-31 14:17:51,463 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-31 14:17:51,464 - matplotlib - DEBUG - interactive is False
2025-07-31 14:17:51,464 - matplotlib - DEBUG - platform is win32
2025-07-31 14:17:51,512 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-31 14:17:51,516 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-31 14:17:52,522 - __main__ - INFO - Initializing database...
2025-07-31 14:17:52,523 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-31 14:17:52,523 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("users")
2025-07-31 14:17:52,524 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,524 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("customers")
2025-07-31 14:17:52,525 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,526 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("product_categories")
2025-07-31 14:17:52,526 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,526 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("products")
2025-07-31 14:17:52,526 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,527 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoices")
2025-07-31 14:17:52,527 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,527 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("invoice_lines")
2025-07-31 14:17:52,527 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,527 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("suppliers")
2025-07-31 14:17:52,528 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,528 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_activities")
2025-07-31 14:17:52,528 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,528 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_sessions")
2025-07-31 14:17:52,528 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-31 14:17:52,529 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-31 14:17:52,529 - __main__ - INFO - Database initialized successfully
2025-07-31 14:17:52,529 - __main__ - INFO - Creating application instance
2025-07-31 14:17:52,544 - __main__ - INFO - Creating login window
2025-07-31 14:17:52,555 - passlib.utils.compat - DEBUG - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-31 14:17:52,556 - passlib.utils.compat - DEBUG - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-31 14:17:52,556 - passlib.utils.compat - DEBUG - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-31 14:17:52,602 - passlib.registry - DEBUG - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-31 14:17:52,661 - passlib.handlers.bcrypt - WARNING - (trapped) error reading bcrypt version
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\site-packages\passlib\handlers\bcrypt.py", line 620, in _load_backend_mixin
    version = _bcrypt.__about__.__version__
              ^^^^^^^^^^^^^^^^^
AttributeError: module 'bcrypt' has no attribute '__about__'
2025-07-31 14:17:52,661 - passlib.handlers.bcrypt - DEBUG - detected 'bcrypt' backend, version '<unknown>'
2025-07-31 14:17:52,662 - passlib.handlers.bcrypt - DEBUG - 'bcrypt' backend lacks $2$ support, enabling workaround
2025-07-31 14:17:52,869 - __main__ - INFO - Login window created successfully
2025-07-31 14:17:52,928 - __main__ - INFO - Login window shown
2025-07-31 14:17:52,928 - __main__ - INFO - Starting application event loop
