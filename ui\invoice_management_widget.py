from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QLineEdit, QDateEdit, QTableWidget, QTableWidgetItem, QHeaderView, QSpacerItem, QSizePolicy, QDialog, QGroupBox, QFormLayout, QTextEdit, QComboBox, QDateTimeEdit, QDoubleSpinBox, QFrame)
from PyQt6.QtCore import Qt, QDate, QSize
from PyQt6.QtGui import QIcon, QIntValidator
from ui.dark_theme import get_dark_theme_stylesheet
import os
from sqlalchemy.orm import sessionmaker
from database.session import engine
from sqlalchemy.orm import joinedload
from models.customer import Customer
from models.supplier import Supplier
import pytz
from datetime import datetime
from models.invoice import Invoice
from sqlalchemy import func
import re
from sqlalchemy.exc import IntegrityError
from PyQt6.QtWidgets import QMessageBox

class InvoiceDashboardWidget(QWidget):
    def __init__(self, supplier_mode=False):
        super().__init__()
        self.supplier_mode = supplier_mode
        self.setObjectName("InvoiceDashboardWidget")
        self.setStyleSheet(get_dark_theme_stylesheet())
        self.session = sessionmaker(bind=engine)()
        self.setup_ui()
        self.load_invoices()
        self.create_btn.clicked.connect(self.open_create_invoice_dialog)
        self.search_bar.textChanged.connect(self.filter_invoices)

    def setup_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # --- Summary Cards Row ---
        cards_layout = QHBoxLayout()
        for title in ["All Invoice", "Draft", "Open Invoice", "Overdue", "Paid"]:
            card = self.create_summary_card(title)
            cards_layout.addWidget(card)
        cards_layout.addStretch()
        main_layout.addLayout(cards_layout)

        # --- Toolbar ---
        toolbar_layout = QHBoxLayout()
        self.search_bar = QLineEdit()
        self.search_bar.setPlaceholderText("Search")
        self.search_bar.setFixedWidth(200)
        # Supplier filter dropdown
        self.supplier_filter = QComboBox()
        self.supplier_filter.setFixedWidth(200)
        self.supplier_filter.addItem("All Suppliers")
        self.populate_supplier_filter()
        self.supplier_filter.currentIndexChanged.connect(self.filter_invoices)
        # Remove 'More filters' button
        toolbar_layout.addWidget(self.search_bar)
        toolbar_layout.addWidget(self.supplier_filter)
        toolbar_layout.addSpacerItem(QSpacerItem(40, 20, QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum))
        self.create_btn = QPushButton("+ Create Invoice")
        toolbar_layout.addWidget(self.create_btn)
        main_layout.addLayout(toolbar_layout)

        # --- Invoice Table ---
        self.table = QTableWidget(0, 7)  # 7 columns: Invoice, Supplier, Amount, Date, Notes, Commission Amount, Actions
        self.table.setHorizontalHeaderLabels([
            "Invoice Number", "Supplier", "Amount", "Date", "Notes", "Commission Amount", "Actions"
        ])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeMode.Fixed)  # Actions column
        self.table.setColumnWidth(6, 90)  # Set minimum width for Actions column
        self.table.verticalHeader().setVisible(False)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QTableWidget.EditTrigger.NoEditTriggers)
        self.table.setSortingEnabled(True)  # Enable sorting for all columns
        self.table.cellDoubleClicked.connect(self.handle_table_double_click)
        main_layout.addWidget(self.table)

        # --- Pagination ---
        pagination_layout = QHBoxLayout()
        self.prev_btn = QPushButton("Previous")
        self.next_btn = QPushButton("Next")
        self.page_label = QLabel("Page 1 of 10")
        pagination_layout.addWidget(self.prev_btn)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.page_label)
        pagination_layout.addStretch()
        pagination_layout.addWidget(self.next_btn)
        main_layout.addLayout(pagination_layout)

    def create_summary_card(self, title):
        card = QWidget()
        card.setFixedSize(180, 90)
        card.setStyleSheet("""
            background-color: #3c3c3c;
            border-radius: 10px;
            padding: 12px;
        """)
        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(2)
        label = QLabel(title)
        label.setStyleSheet("font-size: 13px; color: #cccccc;")
        value = QLabel("$0.00")
        value.setStyleSheet("font-size: 26px; font-weight: bold; color: #ffffff;")
        value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        count = QLabel("0")
        count.setStyleSheet("font-size: 14px; color: #4fc3f7; font-weight: bold;")
        count.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label, alignment=Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(value, alignment=Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(count, alignment=Qt.AlignmentFlag.AlignCenter)
        layout.addStretch()
        return card

    def load_invoices(self):
        self.session.expire_all()  # Ensure session is always up-to-date
        self.table.setRowCount(0)
        # Get filter values
        search_text = self.search_bar.text().strip().lower()
        supplier_filter = self.supplier_filter.currentText() if hasattr(self, 'supplier_filter') else "All Suppliers"
        if self.supplier_mode:
            invoices = (
                self.session.query(Invoice)
                .options(joinedload(Invoice.supplier))
                .filter(Invoice.supplier_id != None)
                .order_by(Invoice.created_at.desc())
                .all()
            )
        else:
            invoices = (
                self.session.query(Invoice)
                .options(joinedload(Invoice.customer))
                .filter(Invoice.customer_id != None)
                .order_by(Invoice.created_at.desc())
                .all()
            )
        filtered_invoices = []
        for invoice in invoices:
            # Supplier name
            supplier_name = invoice.supplier.name.lower() if invoice.supplier and invoice.supplier.name else ""
            # Invoice number
            invoice_number = invoice.invoice_number.lower() if invoice.invoice_number else ""
            # Date
            invoice_date = invoice.invoice_date.strftime("%Y-%m-%d") if invoice.invoice_date else ""
            # Filter by supplier
            if supplier_filter != "All Suppliers" and supplier_name != supplier_filter.lower():
                continue
            # Filter by search text (invoice number, supplier, date)
            if search_text:
                if (search_text not in invoice_number and
                    search_text not in supplier_name and
                    search_text not in invoice_date):
                    continue
            filtered_invoices.append(invoice)
        for invoice in filtered_invoices:
            row = self.table.rowCount()
            self.table.insertRow(row)
            # Invoice Number
            item0 = QTableWidgetItem(invoice.invoice_number or "-")
            item0.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 0, item0)
            # Supplier (name only)
            supplier_display = invoice.supplier.name if invoice.supplier and invoice.supplier.name else "-"
            item1 = QTableWidgetItem(supplier_display)
            item1.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 1, item1)
            # Amount
            amount_val = str(invoice.total_amount) if invoice.total_amount is not None else "-"
            item2 = QTableWidgetItem(amount_val)
            item2.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 2, item2)
            # Date
            date_val = invoice.invoice_date.strftime("%Y-%m-%d") if invoice.invoice_date else "-"
            item3 = QTableWidgetItem(date_val)
            item3.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 3, item3)
            # Notes
            notes_val = invoice.notes if invoice.notes else "-"
            item4 = QTableWidgetItem(notes_val)
            item4.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 4, item4)
            # Commission Amount
            try:
                commission_amount = float(invoice.commission_amount) if invoice.commission_amount is not None else float(invoice.total_amount or 0) * float(invoice.commission_rate or 0) / 100
            except Exception:
                commission_amount = 0.0
            item5 = QTableWidgetItem(f"{commission_amount:.2f}")
            item5.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setItem(row, 5, item5)
            # Actions: Always add, even if data is missing
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(4)
            # Edit icon
            edit_icon_path = os.path.join("ui", "resources", "edit.png")
            edit_btn = QPushButton()
            if os.path.exists(edit_icon_path):
                edit_btn.setIcon(QIcon(edit_icon_path))
                edit_btn.setIconSize(QSize(18, 18))
                edit_btn.setText("")
            else:
                edit_btn.setText("E")
                edit_btn.setStyleSheet("color: #fff; background: #1976d2; border-radius: 6px; font-weight: bold;")
            edit_btn.setProperty("invoice_number", invoice.invoice_number)
            edit_btn.setStyleSheet(edit_btn.styleSheet() +
                "QPushButton { background: #232b3e; border: 1.5px solid #4fc3f7; padding: 0 4px; margin: 0; min-width: 28px; min-height: 28px; max-width: 28px; max-height: 28px; } "
                "QPushButton:hover { background: #2196f3; border: 2px solid #fff; }"
            )
            edit_btn.clicked.connect(lambda _, inv_num=invoice.invoice_number: self.open_edit_invoice_dialog(inv_num))
            actions_layout.addWidget(edit_btn)
            # Delete icon
            delete_icon_path = os.path.join("ui", "resources", "delete.png")
            delete_btn = QPushButton()
            if os.path.exists(delete_icon_path):
                delete_btn.setIcon(QIcon(delete_icon_path))
                delete_btn.setIconSize(QSize(18, 18))
                delete_btn.setText("")
            else:
                delete_btn.setText("D")
                delete_btn.setStyleSheet("color: #fff; background: #a00; border-radius: 6px; font-weight: bold;")
            delete_btn.setProperty("invoice_number", invoice.invoice_number)
            delete_btn.setStyleSheet(delete_btn.styleSheet() +
                "QPushButton { background: #a00; border: 1.5px solid #fff; padding: 0 4px; margin: 0; min-width: 28px; min-height: 28px; max-width: 28px; max-height: 28px; } "
                "QPushButton:hover { background: #d32f2f; border: 2px solid #fff; }"
            )
            delete_btn.clicked.connect(lambda _, inv_num=invoice.invoice_number: self.delete_invoice(inv_num))
            actions_layout.addWidget(delete_btn)
            actions_layout.addStretch()
            actions_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.table.setCellWidget(row, 6, actions_widget)
            # Debug print for missing data
            if supplier_display == "-" or amount_val == "-" or date_val == "-":
                print(f"DEBUG: Invoice {invoice.invoice_number} has missing data. Supplier: {supplier_display}, Amount: {amount_val}, Date: {date_val}")

    def open_create_invoice_dialog(self):
        dialog = CreateInvoiceDialog(self, supplier_mode=self.supplier_mode)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.load_invoices()

    def handle_table_double_click(self, row, col):
        if col == 0:  # Invoice Number column
            invoice_number = self.table.item(row, 0).text()
            invoice = self.session.query(Invoice).filter(Invoice.invoice_number == invoice_number).first()
            if invoice:
                dlg = InvoiceDetailDialog(invoice, self)
                dlg.exec()

    def populate_supplier_filter(self):
        # Populate supplier filter dropdown with all suppliers from DB
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.supplier_filter.addItem(supplier.name)

    def filter_invoices(self):
        # This will be implemented in the next step to filter table rows based on supplier and search
        self.load_invoices()

    def open_edit_invoice_dialog(self, invoice_number):
        invoice = self.session.query(Invoice).filter(Invoice.invoice_number == invoice_number).first()
        if invoice:
            dialog = EditInvoiceDialog(invoice, self, supplier_mode=self.supplier_mode)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                self.load_invoices()
        else:
            QMessageBox.warning(self, "Invoice Not Found", f"Invoice '{invoice_number}' could not be found or is corrupted.")

    def delete_invoice(self, invoice_number):
        invoice = self.session.query(Invoice).filter(Invoice.invoice_number == invoice_number).first()
        if not invoice:
            QMessageBox.warning(self, "Invoice Not Found", f"Invoice '{invoice_number}' could not be found or is corrupted.")
            return
        reply = QMessageBox.question(self, "Delete Invoice", f"Are you sure you want to delete invoice {invoice_number}?", QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
        if reply == QMessageBox.StandardButton.Yes:
            self.session.delete(invoice)
            self.session.commit()
            self.load_invoices()

class CreateInvoiceDialog(QDialog):
    def __init__(self, parent=None, supplier_mode=False):
        super().__init__(parent)
        self.supplier_mode = supplier_mode
        self.setWindowTitle("Create Invoice")
        self.setModal(True)
        self.setMinimumSize(900, 600)
        self.setStyleSheet(get_dark_theme_stylesheet())
        # Use parent's session if available, else create a new one
        if parent and hasattr(parent, 'session'):
            self.session = parent.session
        else:
            from sqlalchemy.orm import sessionmaker
            from database.session import engine
            self.session = sessionmaker(bind=engine)()
        self.customers = []
        self.setup_ui()
        self.showMaximized()  # Open in windowed full screen
        # Always generate invoice number for the default selection
        self.invoice_number.setReadOnly(True)
        if self.customer_combo.count() > 1:
            self.on_customer_selected(self.customer_combo.currentIndex())

    def setup_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(24)

        # --- Left: Invoice Details ---
        left_col = QVBoxLayout()
        left_col.setSpacing(16)
        invoice_details = QGroupBox("Invoice Details")
        invoice_details.setStyleSheet("QGroupBox { font-weight: bold; font-size: 16px; color: #4fc3f7; border: none; }")
        invoice_form = QFormLayout()
        invoice_form.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)
        self.invoice_number = QLineEdit()
        self.invoice_number.setPlaceholderText("IN-#1234-2024")
        invoice_form.addRow("Invoice Number", self.invoice_number)
        invoice_details.setLayout(invoice_form)
        left_col.addWidget(invoice_details)

        # --- Right: Client Details & Summary ---
        right_col = QVBoxLayout()
        right_col.setSpacing(16)
        max_right_width = 350
        # Wrap right_col in a QWidget to set max width
        right_col_widget = QWidget()
        right_col_widget.setLayout(right_col)
        right_col_widget.setMaximumWidth(max_right_width)
        client_details = QGroupBox("Client Details")
        client_details.setStyleSheet("QGroupBox { font-weight: bold; font-size: 16px; color: #4fc3f7; border: none; }")
        client_form = QFormLayout()

        # Customer/Supplier dropdown with search
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        if self.supplier_mode:
            self.customer_combo.addItem("Select Supplier")
            self.load_suppliers()
        else:
            self.customer_combo.addItem("Select Customer")
            self.load_customers()
        self.customer_combo.currentIndexChanged.connect(self.on_customer_selected)
        client_form.addRow("Supplier" if self.supplier_mode else "Customer", self.customer_combo)

        # Client fields (read-only, auto-filled, only needed fields)
        self.client_email = QLineEdit(); self.client_email.setReadOnly(True)
        self.client_address = QLineEdit(); self.client_address.setReadOnly(True)
        self.client_state = QLineEdit(); self.client_state.setReadOnly(True)
        self.client_contact = QLineEdit(); self.client_contact.setReadOnly(True)
        client_form.addRow("Mail Address", self.client_email)
        client_form.addRow("Address", self.client_address)
        client_form.addRow("State/country", self.client_state)
        client_form.addRow("Contact", self.client_contact)
        client_details.setLayout(client_form)
        right_col.addWidget(client_details)

        # Summary
        summary_box = QGroupBox()
        summary_layout = QFormLayout()
        self.subtotal = QLabel()
        # Always create self.discount as QDoubleSpinBox before any usage
        self.discount = QDoubleSpinBox()
        self.discount.setRange(0, 100)
        self.discount.setDecimals(2)
        self.discount.setSuffix(" %")
        self.discount.setFixedWidth(80)
        self.discount.setToolTip("Enter discount percentage for this invoice.")
        self.discount.valueChanged.connect(lambda: self.update_totals())
        # Ensure Commission Rate input matches Discount input
        self.commission_rate = QDoubleSpinBox()
        self.commission_rate.setRange(0, 100)
        self.commission_rate.setDecimals(2)
        self.commission_rate.setSuffix(" %")
        self.commission_rate.setFixedWidth(80)
        self.commission_rate.setToolTip("Enter commission rate for this supplier.")
        self.commission_rate.valueChanged.connect(lambda: self.update_totals())
        summary_layout.addRow("Subtotal:", self.subtotal)
        summary_layout.addRow("Discount (%)", self.discount)
        summary_layout.addRow("Commission Rate (%)", self.commission_rate)
        self.commission_amount = QLabel()
        self.commission_amount.setAlignment(Qt.AlignmentFlag.AlignRight)
        summary_layout.addRow("Commission Amount:", self.commission_amount)
        self.final_amount = QLabel("0.00")
        self.final_amount.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.final_amount.setStyleSheet("font-weight: bold; font-size: 16px;")
        summary_layout.addRow(QLabel("<b>Final Amount</b>"), self.final_amount)
        summary_box.setLayout(summary_layout)
        right_col.addWidget(summary_box)
        items_label = QLabel("Items")
        items_label.setStyleSheet("font-weight: bold; font-size: 15px; color: #4fc3f7;")
        left_col.addWidget(items_label)
        self.items_table = QTableWidget(0, 8)
        self.items_table.setHorizontalHeaderLabels([
            "Item Name", "Qty", "Qty Unit", "Unit Price", "Price Currency", "Total Price", "Description", ""
        ])
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)           # Item Name (wide)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents) # Qty (compact)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents) # Qty Unit (compact)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents) # Unit Price (compact)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents) # Price Currency (compact)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents) # Total Price (compact)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)          # Description (wide)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)            # Delete btn (small)
        header.resizeSection(7, 48)
        header.setStretchLastSection(False)
        self.items_table.verticalHeader().setDefaultSectionSize(36)  # Set default row height for readability
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("QTableWidget { selection-background-color: #444; alternate-background-color: #333; background: #222; color: #fff; font-size: 15px; } QTableWidget::item:selected { background: #555; } QHeaderView::section { background: #222; color: #fff; font-size: 15px; } QLineEdit { background: #222; color: #fff; font-size: 15px; border: none; padding: 0 4px; }")
        self.items_table.setMinimumHeight(200)
        font = self.items_table.font()
        font.setPointSize(13)
        self.items_table.setFont(font)
        self.items_table.itemChanged.connect(lambda item: self.update_totals(item))
        left_col.addWidget(self.items_table)
        # Initialize summary labels before adding item rows or updating totals
        self.subtotal = QLabel()
        self.total = QLabel()
        self.commission_amount = QLabel()
        # Add these to the summary layout at the appropriate place
        summary_layout.addRow("Subtotal:", self.subtotal)
        summary_layout.addRow("Total:", self.total)
        summary_layout.addRow("Commission Amount:", self.commission_amount)
        # Now it's safe to add item rows and update totals
        self.add_item_row()
        self.add_item_btn = QPushButton("Add Item")
        self.add_item_btn.setIcon(QIcon(os.path.join("ui", "resources", "sales.png")))
        self.add_item_btn.clicked.connect(self.add_item_row)
        left_col.addWidget(self.add_item_btn, alignment=Qt.AlignmentFlag.AlignLeft)
        self.notes = QTextEdit()
        self.notes.setPlaceholderText("Write a note & Service descriptions.")
        self.notes.setFixedHeight(40)  # Reduce note box height
        left_col.addWidget(self.notes)
        left_col.addStretch()
        right_col.addStretch()
        main_layout.addLayout(left_col, 3)
        main_layout.addWidget(right_col_widget)

        # BUTTONS BOX (move here, styled with red border for now)
        from PyQt6.QtWidgets import QFrame
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("QFrame { border: none; background: transparent; }")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(8, 8, 8, 8)
        button_style_create = (
            "QPushButton {"
            "  background-color: #1976d2;"
            "  color: #fff;"
            "  border: 1.5px solid #1976d2;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
            "QPushButton:hover, QPushButton:focus {"
            "  background-color: #2196f3;"
            "  border: 2px solid #4fc3f7;"
            "}"
            "QPushButton:disabled {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "}"
        )
        button_style_print = (
            "QPushButton {"
            "  background-color: #263a53;"
            "  color: #fff;"
            "  border: 1.5px solid #4fc3f7;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
            "QPushButton:hover, QPushButton:focus {"
            "  background-color: #1976d2;"
            "  border: 2px solid #4fc3f7;"
            "}"
            "QPushButton:disabled {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "}"
        )
        button_style_draft = (
            "QPushButton {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
        )
        button_width = 100
        buttons_layout.setSpacing(16)
        self.create_invoice_btn = QPushButton("Create")
        self.create_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "sales.png")))
        self.create_invoice_btn.setIconSize(QSize(20, 20))
        self.create_invoice_btn.setFixedHeight(40)
        self.create_invoice_btn.setFixedWidth(button_width)
        self.create_invoice_btn.setStyleSheet(button_style_create)
        self.create_invoice_btn.clicked.connect(self.save_invoice)
        buttons_layout.addWidget(self.create_invoice_btn)
        self.draft_invoice_btn = QPushButton("Draft")
        self.draft_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "dashboard.png")))
        self.draft_invoice_btn.setIconSize(QSize(20, 20))
        self.draft_invoice_btn.setFixedHeight(40)
        self.draft_invoice_btn.setFixedWidth(button_width)
        self.draft_invoice_btn.setStyleSheet(button_style_draft)
        self.draft_invoice_btn.setEnabled(False)
        buttons_layout.addWidget(self.draft_invoice_btn)
        self.print_invoice_btn = QPushButton("Print")
        self.print_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "reports.png")))
        self.print_invoice_btn.setIconSize(QSize(20, 20))
        self.print_invoice_btn.setFixedHeight(40)
        self.print_invoice_btn.setFixedWidth(button_width)
        self.print_invoice_btn.setStyleSheet(button_style_print)
        self.print_invoice_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_invoice_btn)
        right_col.addWidget(buttons_frame)
        right_col.addStretch()
        main_layout.addLayout(left_col, 3)
        main_layout.addWidget(right_col_widget)

        # Force custom colors for spinbox buttons (green up, red down) with !important and specific selectors
        spinbox_style = """
        QDoubleSpinBox {
            background: #232323;
            color: #fff;
            border: 1px solid #444;
            border-radius: 6px;
            padding-right: 24px;
            font-size: 15px;
        }
        QDoubleSpinBox::up-button, QAbstractSpinBox::up-button {
            background: #2ecc40 !important;
            border-left: 1px solid #444 !important;
            border-top: 1px solid #444 !important;
            border-radius: 0 6px 0 0 !important;
            min-width: 18px;
            max-width: 18px;
        }
        QDoubleSpinBox::up-button:hover, QAbstractSpinBox::up-button:hover {
            background: #27ae60 !important;
        }
        QDoubleSpinBox::down-button, QAbstractSpinBox::down-button {
            background: #ff4136 !important;
            border-left: 1px solid #444 !important;
            border-bottom: 1px solid #444 !important;
            border-radius: 0 0 6px 0 !important;
            min-width: 18px;
            max-width: 18px;
        }
        QDoubleSpinBox::down-button:hover, QAbstractSpinBox::down-button:hover {
            background: #c0392b !important;
        }
        QDoubleSpinBox::up-arrow, QAbstractSpinBox::up-arrow {
            background: transparent !important;
            color: #fff !important;
            width: 10px;
            height: 10px;
        }
        QDoubleSpinBox::down-arrow, QAbstractSpinBox::down-arrow {
            background: transparent !important;
            color: #fff !important;
            width: 10px;
            height: 10px;
        }
        """
        self.discount.setStyleSheet(spinbox_style)
        self.commission_rate.setStyleSheet(spinbox_style)
        # At the very end of setup_ui, after all UI is set up and combo is populated
        if self.supplier_mode:
            self.on_customer_selected(self.customer_combo.currentIndex())

    def add_item_row(self):
        self.items_table.blockSignals(True)
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)
        # Item Name (editable text)
        item_name = QTableWidgetItem("")
        item_name.setFlags(item_name.flags() | Qt.ItemFlag.ItemIsEditable)
        self.items_table.setItem(row, 0, item_name)
        # Qty (editable number text)
        qty_item = QTableWidgetItem("")
        qty_item.setFlags(qty_item.flags() | Qt.ItemFlag.ItemIsEditable)
        qty_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.items_table.setItem(row, 1, qty_item)
        # Qty Unit (QComboBox)
        from PyQt6.QtWidgets import QComboBox, QSizePolicy, QWidget, QHBoxLayout
        qty_unit_combo = QComboBox()
        qty_unit_combo.addItems(["KG", "Ton", "Carton"])
        qty_unit_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        qty_unit_widget = QWidget()
        qty_unit_layout = QHBoxLayout(qty_unit_widget)
        qty_unit_layout.setContentsMargins(0, 0, 0, 0)
        qty_unit_layout.setSpacing(0)
        qty_unit_layout.addWidget(qty_unit_combo)
        self.items_table.setCellWidget(row, 2, qty_unit_widget)
        # Unit Price (editable number text)
        price_item = QTableWidgetItem("")
        price_item.setFlags(price_item.flags() | Qt.ItemFlag.ItemIsEditable)
        price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
        self.items_table.setItem(row, 3, price_item)
        # Price Currency (QComboBox)
        price_currency_combo = QComboBox()
        price_currency_combo.addItems(["KWD", "USD", "EUR", "OMR", "QAR", "SAR", "AED"])
        price_currency_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        price_currency_widget = QWidget()
        price_currency_layout = QHBoxLayout(price_currency_widget)
        price_currency_layout.setContentsMargins(0, 0, 0, 0)
        price_currency_layout.setSpacing(0)
        price_currency_layout.addWidget(price_currency_combo)
        price_currency_combo.currentIndexChanged.connect(lambda _: self.update_totals())
        self.items_table.setCellWidget(row, 4, price_currency_widget)
        # Total Price (read-only, auto-calculated)
        total_item = QTableWidgetItem("0.00")
        total_item.setFlags(total_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
        self.items_table.setItem(row, 5, total_item)
        # Description (editable text)
        desc_item = QTableWidgetItem("")
        desc_item.setFlags(desc_item.flags() | Qt.ItemFlag.ItemIsEditable)
        self.items_table.setItem(row, 6, desc_item)
        # Delete button in last column (as a QWidget, not QTableWidgetItem)
        self.add_remove_button(row)
        self.items_table.setRowHeight(row, 44)  # Set new row height
        self.items_table.blockSignals(False)
        self.update_totals()  # Ensure totals are recalculated after adding a row

    def update_totals(self, changed_item=None, *args, **kwargs):
        # Only update if the changed item is in Qty or Unit Price columns, or if called without an item (e.g., after row deletion)
        if changed_item is not None:
            col = changed_item.column()
            if col not in [1, 3]:  # Only Qty or Unit Price
                return
        subtotals = {}
        row_count = self.items_table.rowCount()
        if row_count == 0:
            self.subtotal.setText("")
            self.total.setText("")
            return
        for row in range(row_count):
            try:
                qty_item = self.items_table.item(row, 1)
                price_item = self.items_table.item(row, 3)
                price_currency_widget = self.items_table.cellWidget(row, 4)
                price_currency_combo = None
                if price_currency_widget is not None and hasattr(price_currency_widget, 'layout'):
                    layout = price_currency_widget.layout()
                    if layout and layout.count() > 0:
                        price_currency_combo = layout.itemAt(0).widget()
                currency = price_currency_combo.currentText() if price_currency_combo else ""
                qty = float(qty_item.text()) if qty_item and qty_item.text().strip() else 0.0
                price = float(price_item.text()) if price_item and price_item.text().strip() else 0.0
                total = qty * price
                total_item = QTableWidgetItem(f"{total:.2f}")
                total_item.setFlags(total_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.items_table.setItem(row, 5, total_item)
                if currency:
                    subtotals.setdefault(currency, 0.0)
                    subtotals[currency] += total
            except ValueError:
                total_item = QTableWidgetItem("0.00")
                total_item.setFlags(total_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
                self.items_table.setItem(row, 5, total_item)
                continue
        if subtotals:
            subtotal_lines = [f"{subtotals[c]:.2f} {c}" for c in subtotals]
            try:
                discount_percent = float(self.discount.value()) if self.discount.value() else 0.0
            except Exception:
                discount_percent = 0.0
            discount_str = f"{discount_percent:.0f}%"
            self.discount.setValue(discount_percent)  # Always show as value
            total_lines = []
            commission_lines = []
            for c in subtotals:
                discounted = subtotals[c] * (1 - discount_percent / 100)
                commission_rate = float(self.commission_rate.value())
                commission_amt = discounted * (commission_rate / 100)
                final_amt = discounted - commission_amt
                commission_lines.append(f"{commission_amt:.2f} {c}")
                total_lines.append(f"{discounted:.2f} {c}")
                if c == list(subtotals.keys())[0]:
                    self.final_amount.setText(f"{final_amt:.2f} {c}")
            self.commission_amount.setText("\n".join(commission_lines))
            self.subtotal.setText("\n".join(subtotal_lines))
            self.total.setText("\n".join(total_lines))
        else:
            self.subtotal.setText("")
            self.total.setText("")
            self.commission_amount.setText("")

    def load_customers(self):
        try:
            self.customers = self.session.query(Customer).order_by(Customer.name).all()
            self.customer_combo.clear()
            self.customer_combo.addItem("Select Customer")
            for customer in self.customers:
                display_name = customer.name or f"Customer {customer.id}"
                self.customer_combo.addItem(display_name, customer.id)
        except Exception as e:
            print(f"Error loading customers: {e}")

    def load_suppliers(self):
        print("DEBUG: EditInvoiceDialog.load_suppliers called")
        try:
            self.suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
            self.customer_combo.clear()
            self.customer_combo.addItem("Select Supplier")
            for supplier in self.suppliers:
                display_name = supplier.name or f"Supplier {supplier.id}"
                self.customer_combo.addItem(display_name, supplier.id)
        except Exception as e:
            print(f"Error loading suppliers: {e}")

    def on_customer_selected(self, index):
        # Fill client fields based on selected customer/supplier
        if self.supplier_mode:
            if index > 0 and hasattr(self, 'suppliers') and index-1 < len(self.suppliers):
                supplier = self.suppliers[index-1]
                self.client_email.setText(supplier.email or "")
                self.client_address.setText(supplier.address or "")
                self.client_state.setText(supplier.state or "")
                self.client_contact.setText(supplier.contact_person or "")
            else:
                self.client_email.clear()
                self.client_address.clear()
                self.client_state.clear()
                self.client_contact.clear()
        else:
            if index > 0 and hasattr(self, 'customers') and index-1 < len(self.customers):
                customer = self.customers[index-1]
                self.client_email.setText(customer.email or "")
                self.client_address.setText(customer.address or "")
                self.client_state.setText(customer.state or "")
                self.client_contact.setText(customer.contact_person or "")
            else:
                self.client_email.clear()
                self.client_address.clear()
                self.client_state.clear()
                self.client_contact.clear()

    def add_remove_button(self, row):
        from PyQt6.QtWidgets import QPushButton, QWidget, QVBoxLayout
        from PyQt6.QtGui import QIcon
        import os
        btn = QPushButton()
        # Try to use a trash icon from resources, fallback to standard icon, then Unicode
        icon_path = os.path.join("ui", "resources", "delete.png")
        if os.path.exists(icon_path):
            btn.setIcon(QIcon(icon_path))
        else:
            # Try to use a standard Qt trash icon if available
            try:
                from PyQt6.QtGui import QStyle
                btn.setIcon(self.style().standardIcon(getattr(QStyle.StandardPixmap, 'SP_TrashIcon', 0)))
            except Exception:
                btn.setText("✖")  # Fallback to Unicode cross
        btn.setToolTip("Delete this row")
        btn.setFixedSize(32, 32)
        btn.setStyleSheet("background: transparent; color: #fff; border: none; font-size: 18px; padding: 0; margin: 0;")
        btn.clicked.connect(lambda _, r=row: self.remove_item_row(r))
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(btn, alignment=Qt.AlignmentFlag.AlignCenter)
        widget.setLayout(layout)
        self.items_table.setCellWidget(row, 7, widget)

    def remove_item_row(self, row):
        self.items_table.blockSignals(True)
        self.items_table.removeRow(row)
        # Reconnect delete buttons for all rows to correct indices
        for r in range(self.items_table.rowCount()):
            self.add_remove_button(r)
            self.items_table.setRowHeight(r, 44)  # Ensure all rows have correct height
        self.items_table.blockSignals(False)
        self.update_totals()  # Ensure totals are recalculated after removing a row

    def get_next_invoice_number(self, supplier_id, s_code):
        # Use a new session to get and increment the supplier's last_invoice_number atomically
        Session = sessionmaker(bind=engine)
        with Session() as temp_session:
            from models.supplier import Supplier
            supplier = temp_session.query(Supplier).filter(Supplier.id == supplier_id).with_for_update().first()
            if not supplier:
                return f"0001-{s_code}"
            next_num = (supplier.last_invoice_number or 0) + 1
            return f"{next_num:04d}-{s_code}"

    def save_invoice(self):
        # Validate required fields
        customer_index = self.customer_combo.currentIndex()
        if customer_index <= 0:
            QMessageBox.warning(self, "Missing Data", "Please select a customer/supplier.")
            return
        customer = self.customers[customer_index - 1]
        if self.supplier_mode:
            supplier_id = customer.id
            s_code = (getattr(customer, 's_code', None) or "0000").strip().upper()
            # Use new session to get and increment last_invoice_number
            Session = sessionmaker(bind=engine)
            with Session() as temp_session:
                from models.supplier import Supplier
                supplier = temp_session.query(Supplier).filter(Supplier.id == supplier_id).with_for_update().first()
                if not supplier:
                    invoice_number = f"0001-{s_code}"
                else:
                    next_num = (supplier.last_invoice_number or 0) + 1
                    invoice_number = f"{next_num:04d}-{s_code}"
                    supplier.last_invoice_number = next_num
                    temp_session.commit()
            self.invoice_number.setText(invoice_number)
        else:
            invoice_number = self.invoice_number.text().strip()
        # Expire main session before saving
        self.session.expire_all()
        # Now continue with the rest of save logic, using the (re)generated invoice_number
        # Collect items
        items = []
        for row in range(self.items_table.rowCount()):
            name_item = self.items_table.item(row, 0)
            qty_item = self.items_table.item(row, 1)
            qty_unit_widget = self.items_table.cellWidget(row, 2)
            qty_unit_combo = qty_unit_widget.layout().itemAt(0).widget() if qty_unit_widget else None
            price_item = self.items_table.item(row, 3)
            price_currency_widget = self.items_table.cellWidget(row, 4)
            price_currency_combo = price_currency_widget.layout().itemAt(0).widget() if price_currency_widget else None
            total_item = self.items_table.item(row, 5)
            desc_item = self.items_table.item(row, 6)
            if not name_item or not qty_item or not price_item:
                continue
            item = {
                "name": name_item.text().strip(),
                "qty": qty_item.text().strip(),
                "qty_unit": qty_unit_combo.currentText() if qty_unit_combo else "",
                "unit_price": price_item.text().strip(),
                "currency": price_currency_combo.currentText() if price_currency_combo else "",
                "total": total_item.text() if total_item else "0.00",
                "desc": desc_item.text().strip() if desc_item else ""
            }
            if item["name"] and item["qty"] and item["unit_price"]:
                items.append(item)
        if not items:
            QMessageBox.warning(self, "No Items", "Please add at least one item to the invoice.")
            return
        # Collect discount
        try:
            discount = float(self.discount.value())
        except Exception:
            discount = 0.0
        commission_rate = float(self.commission_rate.value())
        commission_amount = 0.0
        try:
            subtotal = sum(float(i["total"]) for i in items)
            discounted = subtotal * (1 - discount/100)
            commission_amount = discounted * (commission_rate/100)
        except Exception:
            pass
        notes = self.notes.toPlainText().strip()
        from models.invoice import Invoice, InvoiceLine, InvoiceStatus
        from models.user import User
        from datetime import datetime
        customer = self.customers[customer_index - 1]
        now = datetime.utcnow()
        try:
            invoice_kwargs = dict(
                invoice_number=invoice_number,
                invoice_date=now,
                due_date=now,  # You may want to add a due date picker
                status=InvoiceStatus.DRAFT,
                is_paid=False,
                subtotal=sum(float(i["total"]) for i in items),
                tax_amount=0.0,
                discount_amount=discount,
                total_amount=subtotal * (1 - discount/100) - commission_amount,
                payment_method=None,
                payment_date=None,
                payment_reference=None,
                notes=notes,
                terms_and_conditions=None,
                user_id=1  # TODO: Replace with actual logged-in user
            )
            if self.supplier_mode:
                invoice_kwargs["supplier_id"] = customer.id
                invoice_kwargs["customer_id"] = None
            else:
                invoice_kwargs["customer_id"] = customer.id
                invoice_kwargs["supplier_id"] = None
            invoice_kwargs["commission_rate"] = commission_rate
            invoice_kwargs["commission_amount"] = commission_amount
            invoice = Invoice(**invoice_kwargs)
            self.session.add(invoice)
            self.session.flush()  # To get invoice.id
            for i in items:
                line = InvoiceLine(
                    invoice_id=invoice.id,
                    product_id=None,  # Not implemented
                    description=i["desc"] or i["name"],
                    quantity=float(i["qty"] or 0),
                    unit_price=float(i["unit_price"] or 0),
                    tax_rate=0.0,
                    discount_percent=0.0
                )
                self.session.add(line)
            self.session.commit()
            self.session.expire_all()  # Ensure session sees new data
            QMessageBox.information(self, "Invoice Created", f"Invoice has been created and saved successfully!\nInvoice Number: {self.invoice_number.text()}")
            self.accept()
            # Refresh parent table if possible
            if self.parent() and hasattr(self.parent(), 'load_invoices'):
                self.parent().session.expire_all()  # Expire parent session before reload
                self.parent().load_invoices()
            # After saving, re-generate the next invoice number for the selected supplier
            if self.supplier_mode:
                self.on_customer_selected(self.customer_combo.currentIndex())
        except IntegrityError as e:
            self.session.rollback()
            QMessageBox.critical(self, "Duplicate Invoice Number", "An invoice with this number already exists for this supplier. Please try again.")
        except Exception as e:
            self.session.rollback()
            QMessageBox.critical(self, "Error", f"Failed to save invoice: {e}")

    def print_invoice(self):
        QMessageBox.information(self, "Print Invoice", "Print feature coming soon.")

class InvoiceDetailDialog(QDialog):
    def __init__(self, invoice, parent=None):
        super().__init__(parent)
        self.invoice = invoice
        self.setWindowTitle(f"Invoice Details - {invoice.invoice_number}")
        self.setMinimumSize(600, 500)
        self.setStyleSheet("""
            QDialog { background: #23272e; }
            QLabel { color: #fff; font-size: 16px; }
            QGroupBox { font-weight: bold; font-size: 18px; color: #4fc3f7; border: none; margin-top: 16px; }
            QFrame#divider { background: #4fc3f7; min-height: 2px; max-height: 2px; }
        """)
        layout = QVBoxLayout(self)
        # Header
        header = QLabel(f"<b>Invoice Number:</b> {invoice.invoice_number}")
        header.setStyleSheet("font-size: 22px; color: #4fc3f7; margin-bottom: 12px;")
        layout.addWidget(header)
        layout.addWidget(self._divider())
        # Supplier
        supplier = invoice.supplier.name if invoice.supplier else "-"
        layout.addWidget(QLabel(f"<b>Supplier:</b> {supplier}"))
        layout.addWidget(QLabel(f"<b>Date:</b> {invoice.invoice_date.strftime('%Y-%m-%d') if invoice.invoice_date else '-'}"))
        layout.addWidget(QLabel(f"<b>Amount:</b> {invoice.total_amount}"))
        layout.addWidget(QLabel(f"<b>Notes:</b> {invoice.notes or '-'}"))
        layout.addWidget(self._divider())
        # Items
        items_group = QGroupBox("Items")
        items_layout = QVBoxLayout(items_group)
        for line in invoice.lines:
            items_layout.addWidget(QLabel(f"{line.description} | Qty: {line.quantity} | Unit Price: {line.unit_price} | Total: {line.total}"))
        layout.addWidget(items_group)
        layout.addWidget(self._divider())
        # Close button
        btn = QPushButton("Close")
        btn.setStyleSheet("background: #4fc3f7; color: #23272e; font-weight: bold; font-size: 16px; border-radius: 8px; padding: 8px 24px;")
        btn.clicked.connect(self.accept)
        layout.addWidget(btn, alignment=Qt.AlignmentFlag.AlignRight)
    def _divider(self):
        from PyQt6.QtWidgets import QFrame
        line = QFrame()
        line.setObjectName("divider")
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        return line

class EditInvoiceDialog(QDialog):
    def __init__(self, invoice, parent=None, supplier_mode=False):
        super().__init__(parent)
        self.supplier_mode = supplier_mode
        self.invoice = invoice
        self.session = parent.session if parent and hasattr(parent, 'session') else None
        self.customers = []
        self.setWindowTitle(f"Edit Invoice: {invoice.invoice_number}")
        self.setModal(True)
        self.setMinimumSize(900, 600)
        self.setStyleSheet(get_dark_theme_stylesheet())
        self.setup_ui()
        self.load_invoice_data()
        self.create_invoice_btn.setText("Save")
        self.create_invoice_btn.clicked.disconnect()
        self.create_invoice_btn.clicked.connect(self.save_invoice)
        self.invoice_number.setReadOnly(True)

    def setup_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(24)

        # --- Left: Invoice Details ---
        left_col = QVBoxLayout()
        left_col.setSpacing(16)
        invoice_details = QGroupBox("Invoice Details")
        invoice_details.setStyleSheet("QGroupBox { font-weight: bold; font-size: 16px; color: #4fc3f7; border: none; }")
        invoice_form = QFormLayout()
        invoice_form.setLabelAlignment(Qt.AlignmentFlag.AlignLeft)
        self.invoice_number = QLineEdit()
        self.invoice_number.setPlaceholderText("IN-#1234-2024")
        invoice_form.addRow("Invoice Number", self.invoice_number)
        invoice_details.setLayout(invoice_form)
        left_col.addWidget(invoice_details)

        # --- Right: Client Details & Summary ---
        right_col = QVBoxLayout()
        right_col.setSpacing(16)
        max_right_width = 350
        # Wrap right_col in a QWidget to set max width
        right_col_widget = QWidget()
        right_col_widget.setLayout(right_col)
        right_col_widget.setMaximumWidth(max_right_width)
        client_details = QGroupBox("Client Details")
        client_details.setStyleSheet("QGroupBox { font-weight: bold; font-size: 16px; color: #4fc3f7; border: none; }")
        client_form = QFormLayout()

        # Customer/Supplier dropdown with search
        self.customer_combo = QComboBox()
        self.customer_combo.setEditable(True)
        if self.supplier_mode:
            self.customer_combo.addItem("Select Supplier")
            self.load_suppliers()
        else:
            self.customer_combo.addItem("Select Customer")
            self.load_customers()
        self.customer_combo.currentIndexChanged.connect(self.on_customer_selected)
        client_form.addRow("Supplier" if self.supplier_mode else "Customer", self.customer_combo)

        # Client fields (read-only, auto-filled, only needed fields)
        self.client_email = QLineEdit(); self.client_email.setReadOnly(True)
        self.client_address = QLineEdit(); self.client_address.setReadOnly(True)
        self.client_state = QLineEdit(); self.client_state.setReadOnly(True)
        self.client_contact = QLineEdit(); self.client_contact.setReadOnly(True)
        client_form.addRow("Mail Address", self.client_email)
        client_form.addRow("Address", self.client_address)
        client_form.addRow("State/country", self.client_state)
        client_form.addRow("Contact", self.client_contact)
        client_details.setLayout(client_form)
        right_col.addWidget(client_details)

        # Summary
        summary_box = QGroupBox()
        summary_layout = QFormLayout()
        self.subtotal = QLabel()
        # Always create self.discount as QDoubleSpinBox before any usage
        self.discount = QDoubleSpinBox()
        self.discount.setRange(0, 100)
        self.discount.setDecimals(2)
        self.discount.setSuffix(" %")
        self.discount.setFixedWidth(80)
        self.discount.setToolTip("Enter discount percentage for this invoice.")
        self.discount.valueChanged.connect(lambda: self.update_totals())
        # Ensure Commission Rate input matches Discount input
        self.commission_rate = QDoubleSpinBox()
        self.commission_rate.setRange(0, 100)
        self.commission_rate.setDecimals(2)
        self.commission_rate.setSuffix(" %")
        self.commission_rate.setFixedWidth(80)
        self.commission_rate.setToolTip("Enter commission rate for this supplier.")
        self.commission_rate.valueChanged.connect(lambda: self.update_totals())
        summary_layout.addRow("Subtotal:", self.subtotal)
        summary_layout.addRow("Discount (%)", self.discount)
        summary_layout.addRow("Commission Rate (%)", self.commission_rate)
        self.commission_amount = QLabel()
        self.commission_amount.setAlignment(Qt.AlignmentFlag.AlignRight)
        summary_layout.addRow("Commission Amount:", self.commission_amount)
        self.final_amount = QLabel("0.00")
        self.final_amount.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.final_amount.setStyleSheet("font-weight: bold; font-size: 16px;")
        summary_layout.addRow(QLabel("<b>Final Amount</b>"), self.final_amount)
        summary_box.setLayout(summary_layout)
        right_col.addWidget(summary_box)
        items_label = QLabel("Items")
        items_label.setStyleSheet("font-weight: bold; font-size: 15px; color: #4fc3f7;")
        left_col.addWidget(items_label)
        self.items_table = QTableWidget(0, 8)
        self.items_table.setHorizontalHeaderLabels([
            "Item Name", "Qty", "Qty Unit", "Unit Price", "Price Currency", "Total Price", "Description", ""
        ])
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)           # Item Name (wide)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents) # Qty (compact)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents) # Qty Unit (compact)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents) # Unit Price (compact)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents) # Price Currency (compact)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents) # Total Price (compact)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)          # Description (wide)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.Fixed)            # Delete btn (small)
        header.resizeSection(7, 48)
        header.setStretchLastSection(False)
        self.items_table.verticalHeader().setDefaultSectionSize(36)  # Set default row height for readability
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setStyleSheet("QTableWidget { selection-background-color: #444; alternate-background-color: #333; background: #222; color: #fff; font-size: 15px; } QTableWidget::item:selected { background: #555; } QHeaderView::section { background: #222; color: #fff; font-size: 15px; } QLineEdit { background: #222; color: #fff; font-size: 15px; border: none; padding: 0 4px; }")
        self.items_table.setMinimumHeight(200)
        font = self.items_table.font()
        font.setPointSize(13)
        self.items_table.setFont(font)
        self.items_table.itemChanged.connect(lambda item: self.update_totals(item))
        left_col.addWidget(self.items_table)
        # Initialize summary labels before adding item rows or updating totals
        self.subtotal = QLabel()
        self.total = QLabel()
        self.commission_amount = QLabel()
        # Add these to the summary layout at the appropriate place
        summary_layout.addRow("Subtotal:", self.subtotal)
        summary_layout.addRow("Total:", self.total)
        summary_layout.addRow("Commission Amount:", self.commission_amount)
        # Now it's safe to add item rows and update totals
        self.add_item_row()
        self.add_item_btn = QPushButton("Add Item")
        self.add_item_btn.setIcon(QIcon(os.path.join("ui", "resources", "sales.png")))
        self.add_item_btn.clicked.connect(self.add_item_row)
        left_col.addWidget(self.add_item_btn, alignment=Qt.AlignmentFlag.AlignLeft)
        self.notes = QTextEdit()
        self.notes.setPlaceholderText("Write a note & Service descriptions.")
        self.notes.setFixedHeight(40)  # Reduce note box height
        left_col.addWidget(self.notes)
        left_col.addStretch()
        right_col.addStretch()
        main_layout.addLayout(left_col, 3)
        main_layout.addWidget(right_col_widget)

        # BUTTONS BOX (move here, styled with red border for now)
        from PyQt6.QtWidgets import QFrame
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("QFrame { border: none; background: transparent; }")
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(8, 8, 8, 8)
        button_style_create = (
            "QPushButton {"
            "  background-color: #1976d2;"
            "  color: #fff;"
            "  border: 1.5px solid #1976d2;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
            "QPushButton:hover, QPushButton:focus {"
            "  background-color: #2196f3;"
            "  border: 2px solid #4fc3f7;"
            "}"
            "QPushButton:disabled {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "}"
        )
        button_style_print = (
            "QPushButton {"
            "  background-color: #263a53;"
            "  color: #fff;"
            "  border: 1.5px solid #4fc3f7;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
            "QPushButton:hover, QPushButton:focus {"
            "  background-color: #1976d2;"
            "  border: 2px solid #4fc3f7;"
            "}"
            "QPushButton:disabled {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "}"
        )
        button_style_draft = (
            "QPushButton {"
            "  background-color: #232b3e;"
            "  color: #888;"
            "  border: 1.5px solid #444;"
            "  border-radius: 8px;"
            "  font-size: 13px;"
            "  padding: 6px 16px;"
            "  font-weight: 600;"
            "  outline: none;"
            "}"
        )
        button_width = 100
        buttons_layout.setSpacing(16)
        self.create_invoice_btn = QPushButton("Create")
        self.create_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "sales.png")))
        self.create_invoice_btn.setIconSize(QSize(20, 20))
        self.create_invoice_btn.setFixedHeight(40)
        self.create_invoice_btn.setFixedWidth(button_width)
        self.create_invoice_btn.setStyleSheet(button_style_create)
        self.create_invoice_btn.clicked.connect(self.save_invoice)
        buttons_layout.addWidget(self.create_invoice_btn)
        self.draft_invoice_btn = QPushButton("Draft")
        self.draft_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "dashboard.png")))
        self.draft_invoice_btn.setIconSize(QSize(20, 20))
        self.draft_invoice_btn.setFixedHeight(40)
        self.draft_invoice_btn.setFixedWidth(button_width)
        self.draft_invoice_btn.setStyleSheet(button_style_draft)
        self.draft_invoice_btn.setEnabled(False)
        buttons_layout.addWidget(self.draft_invoice_btn)
        self.print_invoice_btn = QPushButton("Print")
        self.print_invoice_btn.setIcon(QIcon(os.path.join("ui", "resources", "reports.png")))
        self.print_invoice_btn.setIconSize(QSize(20, 20))
        self.print_invoice_btn.setFixedHeight(40)
        self.print_invoice_btn.setFixedWidth(button_width)
        self.print_invoice_btn.setStyleSheet(button_style_print)
        self.print_invoice_btn.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_invoice_btn)
        right_col.addWidget(buttons_frame)
        right_col.addStretch()
        main_layout.addLayout(left_col, 3)
        main_layout.addWidget(right_col_widget)

        # Force custom colors for spinbox buttons (green up, red down) with !important and specific selectors
        spinbox_style = """
        QDoubleSpinBox {
            background: #232323;
            color: #fff;
            border: 1px solid #444;
            border-radius: 6px;
            padding-right: 24px;
            font-size: 15px;
        }
        QDoubleSpinBox::up-button, QAbstractSpinBox::up-button {
            background: #2ecc40 !important;
            border-left: 1px solid #444 !important;
            border-top: 1px solid #444 !important;
            border-radius: 0 6px 0 0 !important;
            min-width: 18px;
            max-width: 18px;
        }
        QDoubleSpinBox::up-button:hover, QAbstractSpinBox::up-button:hover {
            background: #27ae60 !important;
        }
        QDoubleSpinBox::down-button, QAbstractSpinBox::down-button {
            background: #ff4136 !important;
            border-left: 1px solid #444 !important;
            border-bottom: 1px solid #444 !important;
            border-radius: 0 0 6px 0 !important;
            min-width: 18px;
            max-width: 18px;
        }
        QDoubleSpinBox::down-button:hover, QAbstractSpinBox::down-button:hover {
            background: #c0392b !important;
        }
        QDoubleSpinBox::up-arrow, QAbstractSpinBox::up-arrow {
            background: transparent !important;
            color: #fff !important;
            width: 10px;
            height: 10px;
        }
        QDoubleSpinBox::down-arrow, QAbstractSpinBox::down-arrow {
            background: transparent !important;
            color: #fff !important;
            width: 10px;
            height: 10px;
        }
        """
        self.discount.setStyleSheet(spinbox_style)
        self.commission_rate.setStyleSheet(spinbox_style)
        # At the very end of setup_ui, after all UI is set up and combo is populated
        if self.supplier_mode:
            self.on_customer_selected(self.customer_combo.currentIndex())

    def load_invoice_data(self):
        # Fill all fields with invoice data
        self.invoice_number.setText(self.invoice.invoice_number or "")
        # Set supplier/customer combo
        if self.supplier_mode:
            if self.invoice.supplier:
                idx = self.customer_combo.findText(self.invoice.supplier.name)
                if idx >= 0:
                    self.customer_combo.setCurrentIndex(idx)
        else:
            if self.invoice.customer:
                idx = self.customer_combo.findText(self.invoice.customer.name)
                if idx >= 0:
                    self.customer_combo.setCurrentIndex(idx)
        # Set commission rate and discount
        self.commission_rate.setValue(float(self.invoice.commission_rate or 0))
        self.discount.setValue(float(self.invoice.discount_amount or 0))
        # Set notes
        self.notes.setText(self.invoice.notes or "")
        # Clear and repopulate items table
        self.items_table.blockSignals(True)
        self.items_table.setRowCount(0)
        for line in getattr(self.invoice, 'lines', []):
            row = self.items_table.rowCount()
            self.items_table.insertRow(row)
            # Item Name
            item_name = QTableWidgetItem(line.description or "")
            item_name.setFlags(item_name.flags() | Qt.ItemFlag.ItemIsEditable)
            self.items_table.setItem(row, 0, item_name)
            # Qty
            qty_item = QTableWidgetItem(str(line.quantity or ""))
            qty_item.setFlags(qty_item.flags() | Qt.ItemFlag.ItemIsEditable)
            qty_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.items_table.setItem(row, 1, qty_item)
            # Qty Unit
            from PyQt6.QtWidgets import QComboBox, QSizePolicy, QWidget, QHBoxLayout
            qty_unit_combo = QComboBox()
            qty_unit_combo.addItems(["KG", "Ton", "Carton"])
            if hasattr(line, 'qty_unit') and line.qty_unit:
                idx = qty_unit_combo.findText(line.qty_unit)
                if idx >= 0:
                    qty_unit_combo.setCurrentIndex(idx)
            qty_unit_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            qty_unit_widget = QWidget()
            qty_unit_layout = QHBoxLayout(qty_unit_widget)
            qty_unit_layout.setContentsMargins(0, 0, 0, 0)
            qty_unit_layout.setSpacing(0)
            qty_unit_layout.addWidget(qty_unit_combo)
            self.items_table.setCellWidget(row, 2, qty_unit_widget)
            # Unit Price
            price_item = QTableWidgetItem(str(line.unit_price or ""))
            price_item.setFlags(price_item.flags() | Qt.ItemFlag.ItemIsEditable)
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.items_table.setItem(row, 3, price_item)
            # Price Currency
            price_currency_combo = QComboBox()
            price_currency_combo.addItems(["KWD", "USD", "EUR", "OMR", "QAR", "SAR", "AED"])
            if hasattr(line, 'currency') and line.currency:
                idx = price_currency_combo.findText(line.currency)
                if idx >= 0:
                    price_currency_combo.setCurrentIndex(idx)
            price_currency_combo.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
            price_currency_widget = QWidget()
            price_currency_layout = QHBoxLayout(price_currency_widget)
            price_currency_layout.setContentsMargins(0, 0, 0, 0)
            price_currency_layout.setSpacing(0)
            price_currency_layout.addWidget(price_currency_combo)
            price_currency_combo.currentIndexChanged.connect(lambda _: self.update_totals())
            self.items_table.setCellWidget(row, 4, price_currency_widget)
            # Total Price (auto-calculated)
            try:
                qty = float(line.quantity or 0)
                price = float(line.unit_price or 0)
                total = qty * price
            except Exception:
                total = 0.0
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setFlags(total_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.items_table.setItem(row, 5, total_item)
            # Description
            desc_item = QTableWidgetItem(line.description or "")
            desc_item.setFlags(desc_item.flags() | Qt.ItemFlag.ItemIsEditable)
            self.items_table.setItem(row, 6, desc_item)
            # Delete button
            self.add_remove_button(row)
            self.items_table.setRowHeight(row, 44)
        self.items_table.blockSignals(False)
        self.update_totals()
        # Set summary fields
        self.subtotal.setText(str(self.invoice.subtotal or ""))
        self.total.setText(str(self.invoice.total_amount or ""))
        self.commission_amount.setText(str(self.invoice.commission_amount or ""))
        if hasattr(self, 'final_amount'):
            self.final_amount.setText(str(self.invoice.total_amount or ""))

    def save_invoice(self):
        # Only update the existing invoice, never call session.add for the main invoice
        # Defensive: Invoice number should not be editable, but if it is, ensure last_invoice_number is updated if changed
        customer_index = self.customer_combo.currentIndex()
        if customer_index <= 0:
            QMessageBox.warning(self, "Missing Data", "Please select a customer/supplier.")
            return
        customer = self.customers[customer_index - 1]
        if self.supplier_mode:
            supplier_id = customer.id
            s_code = (getattr(customer, 's_code', None) or "0000").strip().upper()
            invoice_number = self.invoice_number.text().strip()
            # Defensive: If invoice_number is not the latest, update last_invoice_number
            Session = sessionmaker(bind=engine)
            with Session() as temp_session:
                from models.supplier import Supplier
                supplier = temp_session.query(Supplier).filter(Supplier.id == supplier_id).with_for_update().first()
                if supplier:
                    try:
                        num_part = int(invoice_number.split("-")[0])
                        if num_part > (supplier.last_invoice_number or 0):
                            supplier.last_invoice_number = num_part
                            temp_session.commit()
                    except Exception:
                        pass
        else:
            invoice_number = self.invoice_number.text().strip()
        # Continue with the rest of the save logic as before
        # Always reload the invoice from DB to avoid stale state
        invoice = self.session.query(Invoice).filter(Invoice.id == self.invoice.id).first()
        if not invoice:
            QMessageBox.critical(self, "Error", "Invoice not found in database.")
            return
        invoice_number = self.invoice_number.text().strip()
        customer_index = self.customer_combo.currentIndex()
        if not invoice_number or customer_index <= 0:
            QMessageBox.warning(self, "Missing Data", "Please select a customer/supplier.")
            return
        # Defensive: Ensure invoice number is unique (shouldn't change in edit, but check for safety)
        existing = self.session.query(Invoice).filter(Invoice.invoice_number == invoice_number, Invoice.id != invoice.id).first()
        if existing:
            QMessageBox.critical(self, "Duplicate Invoice Number", f"Invoice number '{invoice_number}' already exists for another invoice.")
            return
        # Defensive: Check combo index bounds
        if self.supplier_mode:
            if customer_index - 1 >= len(self.customers) or customer_index - 1 < 0:
                QMessageBox.critical(self, "Error", "Invalid supplier selection.")
                return
            customer = self.customers[customer_index - 1]
            invoice.supplier_id = customer.id
            invoice.customer_id = None
            print(f"[DEBUG] Assigning supplier: {customer.id} - {getattr(customer, 'name', '')}")
        else:
            if customer_index - 1 >= len(self.customers) or customer_index - 1 < 0:
                QMessageBox.critical(self, "Error", "Invalid customer selection.")
                return
            customer = self.customers[customer_index - 1]
            invoice.customer_id = customer.id
            invoice.supplier_id = None
            print(f"[DEBUG] Assigning customer: {customer.id} - {getattr(customer, 'name', '')}")
        # Invoice number is not editable, but set for completeness
        invoice.invoice_number = invoice_number
        from datetime import datetime
        invoice.invoice_date = datetime.utcnow()  # Or use a date picker if available
        invoice.notes = self.notes.toPlainText().strip()
        invoice.commission_rate = float(self.commission_rate.value())
        invoice.discount_amount = float(self.discount.value())
        # Collect items
        items = []
        for row in range(self.items_table.rowCount()):
            name_item = self.items_table.item(row, 0)
            qty_item = self.items_table.item(row, 1)
            qty_unit_widget = self.items_table.cellWidget(row, 2)
            qty_unit_combo = qty_unit_widget.layout().itemAt(0).widget() if qty_unit_widget else None
            price_item = self.items_table.item(row, 3)
            price_currency_widget = self.items_table.cellWidget(row, 4)
            price_currency_combo = price_currency_widget.layout().itemAt(0).widget() if price_currency_widget else None
            total_item = self.items_table.item(row, 5)
            desc_item = self.items_table.item(row, 6)
            if not name_item or not qty_item or not price_item:
                continue
            item = {
                "name": name_item.text().strip(),
                "qty": qty_item.text().strip(),
                "qty_unit": qty_unit_combo.currentText() if qty_unit_combo else "",
                "unit_price": price_item.text().strip(),
                "currency": price_currency_combo.currentText() if price_currency_combo else "",
                "total": total_item.text() if total_item else "0.00",
                "desc": desc_item.text().strip() if desc_item else ""
            }
            if item["name"] and item["qty"] and item["unit_price"]:
                items.append(item)
        if not items:
            QMessageBox.warning(self, "No Items", "Please add at least one item to the invoice.")
            return
        # Update amounts
        try:
            subtotal = sum(float(i["total"]) for i in items)
            discounted = subtotal * (1 - float(self.discount.value())/100)
            commission_amount = discounted * (float(self.commission_rate.value())/100)
        except Exception:
            subtotal = 0.0
            commission_amount = 0.0
        invoice.subtotal = subtotal
        invoice.total_amount = subtotal * (1 - float(self.discount.value())/100) - commission_amount
        invoice.commission_amount = commission_amount
        # Defensive: ensure invoice is loaded and has a valid ID
        if not invoice.id:
            QMessageBox.critical(self, "Error", "Invoice ID is missing. Cannot update invoice lines.")
            return
        # Use ORM relationship to clear lines safely
        invoice.lines.clear()
        self.session.flush()
        # Add new lines
        from models.invoice import InvoiceLine
        for i in items:
            line = InvoiceLine(
                invoice_id=invoice.id,
                product_id=None,
                description=i["desc"] or i["name"],
                quantity=float(i["qty"] or 0),
                unit_price=float(i["unit_price"] or 0),
                tax_rate=0.0,
                discount_percent=0.0
            )
            self.session.add(line)
        self.session.commit()
        self.session.expire_all()  # Ensure session sees new data
        print(f"[DEBUG] Invoice {invoice.id} ({invoice.invoice_number}) saved successfully.")
        QMessageBox.information(self, "Invoice Updated", "Invoice has been updated successfully!")
        self.accept()
        if self.parent() and hasattr(self.parent(), 'load_invoices'):
            self.parent().load_invoices()

# NOTE: It is strongly recommended to add a unique constraint on (invoice_number, supplier_id) in the DB for full safety.
