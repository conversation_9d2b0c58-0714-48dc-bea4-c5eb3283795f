"""
Activity Logger Utility
Provides easy-to-use functions for logging user activities.
"""
import logging
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from database.session import SessionLocal
from models.user_activity import UserActivity, UserSession

logger = logging.getLogger(__name__)

class ActivityLogger:
    """Utility class for logging user activities."""
    
    def __init__(self):
        self.current_user_id = None
        self.current_session_id = None
        self.session_start_time = None
    
    def set_current_user(self, user_id: int, session_id: str = None):
        """Set the current user for activity logging."""
        self.current_user_id = user_id
        self.current_session_id = session_id or str(uuid.uuid4())
        self.session_start_time = datetime.now()
    
    def log_login(self, user_id: int, ip_address: str = None, user_agent: str = None):
        """Log user login activity."""
        session_id = str(uuid.uuid4())
        self.set_current_user(user_id, session_id)
        
        try:
            db = SessionLocal()
            
            # Start user session
            UserSession.start_session(
                db, user_id, session_id, ip_address, user_agent
            )
            
            # Log login activity
            UserActivity.log_activity(
                db, user_id, 'login', 'authentication',
                f"User logged in at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                session_id=session_id, ip_address=ip_address, user_agent=user_agent
            )
            
            logger.info(f"User {user_id} logged in with session {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error logging login activity: {e}")
            return None
        finally:
            db.close()
    
    def log_logout(self, user_id: int = None, session_id: str = None):
        """Log user logout activity."""
        user_id = user_id or self.current_user_id
        session_id = session_id or self.current_session_id
        
        if not user_id or not session_id:
            return
        
        try:
            db = SessionLocal()
            
            # Calculate session duration
            duration_ms = None
            if self.session_start_time:
                duration = datetime.now() - self.session_start_time
                duration_ms = int(duration.total_seconds() * 1000)
            
            # End user session
            UserSession.end_session(db, session_id)
            
            # Log logout activity
            UserActivity.log_activity(
                db, user_id, 'logout', 'authentication',
                f"User logged out at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                session_id=session_id, duration_ms=duration_ms
            )
            
            logger.info(f"User {user_id} logged out from session {session_id}")
            
        except Exception as e:
            logger.error(f"Error logging logout activity: {e}")
        finally:
            db.close()
    
    def log_action(self, action: str, module: str = None, description: str = None,
                  entity_type: str = None, entity_id: int = None,
                  old_values: Dict[str, Any] = None, new_values: Dict[str, Any] = None,
                  user_id: int = None, session_id: str = None):
        """Log a general user action."""
        user_id = user_id or self.current_user_id
        session_id = session_id or self.current_session_id
        
        if not user_id:
            logger.warning("Cannot log activity: no user ID available")
            return
        
        try:
            db = SessionLocal()
            
            # Convert dictionaries to JSON strings
            old_values_json = json.dumps(old_values) if old_values else None
            new_values_json = json.dumps(new_values) if new_values else None
            
            # Update session activity
            if session_id:
                UserSession.update_activity(db, session_id)
            
            # Log the activity
            UserActivity.log_activity(
                db, user_id, action, module, description,
                entity_type, entity_id, session_id,
                old_values=old_values_json, new_values=new_values_json
            )
            
            logger.debug(f"Logged activity: {action} by user {user_id}")
            
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
        finally:
            db.close()
    
    def log_navigation(self, module: str, user_id: int = None):
        """Log navigation to a module."""
        self.log_action(
            action='navigate',
            module=module,
            description=f"Navigated to {module} module",
            user_id=user_id
        )
    
    def log_create(self, entity_type: str, entity_id: int, entity_data: Dict[str, Any],
                  module: str = None, user_id: int = None):
        """Log creation of an entity."""
        self.log_action(
            action='create',
            module=module,
            description=f"Created {entity_type} with ID {entity_id}",
            entity_type=entity_type,
            entity_id=entity_id,
            new_values=entity_data,
            user_id=user_id
        )
    
    def log_update(self, entity_type: str, entity_id: int, 
                  old_data: Dict[str, Any], new_data: Dict[str, Any],
                  module: str = None, user_id: int = None):
        """Log update of an entity."""
        self.log_action(
            action='update',
            module=module,
            description=f"Updated {entity_type} with ID {entity_id}",
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=old_data,
            new_values=new_data,
            user_id=user_id
        )
    
    def log_delete(self, entity_type: str, entity_id: int, entity_data: Dict[str, Any],
                  module: str = None, user_id: int = None):
        """Log deletion of an entity."""
        self.log_action(
            action='delete',
            module=module,
            description=f"Deleted {entity_type} with ID {entity_id}",
            entity_type=entity_type,
            entity_id=entity_id,
            old_values=entity_data,
            user_id=user_id
        )
    
    def log_view(self, entity_type: str, entity_id: int = None,
                module: str = None, user_id: int = None):
        """Log viewing of an entity or module."""
        description = f"Viewed {entity_type}"
        if entity_id:
            description += f" with ID {entity_id}"
        
        self.log_action(
            action='view',
            module=module,
            description=description,
            entity_type=entity_type,
            entity_id=entity_id,
            user_id=user_id
        )
    
    def log_search(self, search_term: str, results_count: int,
                  module: str = None, user_id: int = None):
        """Log search activity."""
        self.log_action(
            action='search',
            module=module,
            description=f"Searched for '{search_term}' - {results_count} results",
            user_id=user_id
        )
    
    def log_export(self, export_type: str, record_count: int,
                  module: str = None, user_id: int = None):
        """Log data export activity."""
        self.log_action(
            action='export',
            module=module,
            description=f"Exported {record_count} records as {export_type}",
            user_id=user_id
        )
    
    def log_import(self, import_type: str, record_count: int,
                  module: str = None, user_id: int = None):
        """Log data import activity."""
        self.log_action(
            action='import',
            module=module,
            description=f"Imported {record_count} records from {import_type}",
            user_id=user_id
        )
    
    def get_user_activities(self, user_id: int = None, limit: int = 50):
        """Get recent activities for a user."""
        user_id = user_id or self.current_user_id
        
        if not user_id:
            return []
        
        try:
            db = SessionLocal()
            activities = db.query(UserActivity).filter_by(
                user_id=user_id
            ).order_by(UserActivity.created_at.desc()).limit(limit).all()
            
            return [
                {
                    'id': activity.id,
                    'action': activity.action,
                    'module': activity.module,
                    'description': activity.description,
                    'entity_type': activity.entity_type,
                    'entity_id': activity.entity_id,
                    'created_at': activity.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    'duration_ms': activity.duration_ms
                }
                for activity in activities
            ]
        except Exception as e:
            logger.error(f"Error getting user activities: {e}")
            return []
        finally:
            db.close()

# Global activity logger instance
activity_logger = ActivityLogger()
