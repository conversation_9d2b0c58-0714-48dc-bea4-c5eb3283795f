#!/usr/bin/env python3
"""
Test script for the dashboard functionality.
"""
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

def test_dashboard_imports():
    """Test that all dashboard components can be imported."""
    print("Testing dashboard imports...")
    
    try:
        from modules.dashboard_analytics import DashboardAnalytics
        print("✅ DashboardAnalytics imported successfully")
        
        from ui.dashboard_widgets import MetricCard, ChartWidget, DataTableWidget, SystemHealthWidget
        print("✅ Dashboard widgets imported successfully")
        
        from ui.dashboard_widget import DashboardWidget
        print("✅ DashboardWidget imported successfully")
        
        from models.user_activity import UserActivity, UserSession
        print("✅ User activity models imported successfully")
        
        from utils.activity_logger import ActivityLogger, activity_logger
        print("✅ Activity logger imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_analytics_service():
    """Test the analytics service."""
    print("\nTesting analytics service...")
    
    try:
        from modules.dashboard_analytics import DashboardAnalytics
        
        analytics = DashboardAnalytics()
        
        # Test individual methods
        sales_metrics = analytics.get_sales_metrics()
        print(f"✅ Sales metrics: {len(sales_metrics)} fields")
        
        invoice_status = analytics.get_invoice_status_metrics()
        print(f"✅ Invoice status: {len(invoice_status)} fields")
        
        system_health = analytics.get_system_health()
        print(f"✅ System health: {len(system_health)} fields")
        
        # Test comprehensive data
        all_data = analytics.get_all_dashboard_data()
        print(f"✅ All dashboard data: {len(all_data)} sections")
        
        analytics.close_session()
        return True
        
    except Exception as e:
        print(f"❌ Analytics service error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_models():
    """Test database models creation."""
    print("\nTesting database models...")
    
    try:
        from database.session import init_db
        
        # Initialize database with new models
        result = init_db()
        if result:
            print("✅ Database initialized with new models")
        else:
            print("❌ Database initialization failed")
            return False
        
        # Test model imports
        from models.user_activity import UserActivity, UserSession
        print("✅ User activity models available")
        
        return True
        
    except Exception as e:
        print(f"❌ Database models error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_activity_logger():
    """Test activity logger functionality."""
    print("\nTesting activity logger...")
    
    try:
        from utils.activity_logger import ActivityLogger
        
        logger = ActivityLogger()
        
        # Test setting user (without actual database operations)
        logger.set_current_user(1, "test-session-123")
        print("✅ Activity logger user set")
        
        # Test getting activities (should handle gracefully if no data)
        activities = logger.get_user_activities(1)
        print(f"✅ Retrieved {len(activities)} activities")
        
        return True
        
    except Exception as e:
        print(f"❌ Activity logger error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all dashboard tests."""
    print("🔍 Dashboard Functionality Test")
    print("=" * 40)
    
    tests = [
        ("Dashboard Imports", test_dashboard_imports),
        ("Analytics Service", test_analytics_service),
        ("Database Models", test_database_models),
        ("Activity Logger", test_activity_logger),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 DASHBOARD TEST SUMMARY")
    print("=" * 40)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 40)
    if all_passed:
        print("🎉 ALL DASHBOARD TESTS PASSED!")
        print("\nThe comprehensive dashboard is ready to use with:")
        print("• Real-time sales and financial analytics")
        print("• Interactive charts and visualizations")
        print("• User activity tracking and logging")
        print("• System health monitoring")
        print("• Customer and supplier analytics")
        print("• Live data refresh capabilities")
        print("• 🧪 TEST DATA INDICATORS - Clearly marked sample data")
        print("• Sample data generation when real data is not available")
        print("\nTo see the dashboard, run the main application:")
        print("  python main.py")
        print("\nNOTE: The dashboard will show sample/test data clearly marked with")
        print("🧪 indicators when real business data is not available.")
    else:
        print("⚠️  SOME DASHBOARD TESTS FAILED!")
        print("Please check the errors above and fix any issues.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
