"""
Dashboard UI Widgets
Individual widget components for the dashboard.
"""
import logging
from typing import Dict, List, Any
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, 
    QProgressBar, QTableWidget, QTableWidgetItem, QHeaderView,
    QScrollArea, QGridLayout, QPushButton, QSizePolicy
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QPalette, QColor
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import seaborn as sns

logger = logging.getLogger(__name__)

class MetricCard(QFrame):
    """A card widget for displaying key metrics."""

    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "#3498db"):
        super().__init__()
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: #2C2C2C;
                border: 1px solid #404040;
                border-radius: 8px;
                padding: 12px;
            }}
            QLabel {{
                color: #FFFFFF;
                border: none;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(6)

        # Title
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 11))
        title_label.setStyleSheet("color: #CCCCCC;")
        title_label.setWordWrap(True)
        title_label.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.addWidget(title_label)

        # Value
        self.value_label = QLabel(value)
        self.value_label.setFont(QFont("Segoe UI", 20, QFont.Weight.Bold))
        self.value_label.setStyleSheet(f"color: {color};")
        self.value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.value_label.setWordWrap(True)
        self.value_label.setMinimumHeight(30)
        layout.addWidget(self.value_label)

        # Subtitle
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setFont(QFont("Segoe UI", 9))
            subtitle_label.setStyleSheet("color: #999999;")
            subtitle_label.setWordWrap(True)
            subtitle_label.setAlignment(Qt.AlignmentFlag.AlignBottom)
            layout.addWidget(subtitle_label)

        # Set responsive sizing
        self.setMinimumHeight(90)
        self.setMaximumHeight(140)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
    
    def update_value(self, value: str):
        """Update the metric value."""
        self.value_label.setText(value)

class ChartWidget(QWidget):
    """Widget for displaying matplotlib charts."""

    def __init__(self, title: str = "Chart"):
        super().__init__()
        self.title = title
        self.has_test_data = False
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the chart widget UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title with test data indicator
        title_layout = QHBoxLayout()

        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        self.title_label.setStyleSheet("color: #FFFFFF; margin-bottom: 10px;")
        title_layout.addWidget(self.title_label)

        # Test data indicator (initially hidden)
        self.test_indicator = QLabel("🧪 SAMPLE DATA")
        self.test_indicator.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
        self.test_indicator.setStyleSheet("""
            color: #f39c12;
            background-color: #2C2C2C;
            border: 1px solid #f39c12;
            border-radius: 3px;
            padding: 2px 6px;
            margin-left: 10px;
        """)
        self.test_indicator.hide()
        title_layout.addWidget(self.test_indicator)

        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # Chart
        self.figure = Figure(figsize=(6, 3), facecolor='#2C2C2C', tight_layout=True)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setStyleSheet("background-color: #2C2C2C;")
        self.canvas.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.canvas)
        
        # Set dark theme for matplotlib
        plt.style.use('dark_background')
    
    def clear_chart(self):
        """Clear the chart."""
        self.figure.clear()
        self.canvas.draw()
    
    def plot_line_chart(self, data: List[Dict], x_key: str, y_key: str, title: str = ""):
        """Plot a line chart."""
        self.figure.clear()
        ax = self.figure.add_subplot(111)

        if data:
            x_values = [item[x_key] for item in data]
            y_values = [item[y_key] for item in data]

            ax.plot(x_values, y_values, color='#3498db', linewidth=2, marker='o', markersize=4)
            ax.set_title(title, color='white', fontsize=12, pad=10)
            ax.tick_params(colors='white', labelsize=9)
            ax.grid(True, alpha=0.3, color='white')

            # Format y-axis for currency if needed
            if y_key.lower() in ['revenue', 'amount', 'total_revenue', 'total_amount']:
                ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))

            # Rotate x-axis labels for better readability
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontsize=9)

            # Set background color
            ax.set_facecolor('#2C2C2C')

        self.figure.tight_layout(pad=1.0)
        self.canvas.draw()
    
    def plot_bar_chart(self, data: List[Dict], x_key: str, y_key: str, title: str = ""):
        """Plot a bar chart."""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        
        if data:
            x_values = [item[x_key] for item in data]
            y_values = [item[y_key] for item in data]
            
            bars = ax.bar(x_values, y_values, color='#e74c3c', alpha=0.8)
            ax.set_title(title, color='white', fontsize=10)
            ax.tick_params(colors='white', labelsize=8)
            
            # Rotate x-axis labels for better readability
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        
        self.figure.tight_layout()
        self.canvas.draw()
    
    def plot_pie_chart(self, data: List[Dict], label_key: str, value_key: str, title: str = ""):
        """Plot a pie chart."""
        self.figure.clear()
        ax = self.figure.add_subplot(111)

        if data:
            labels = [item[label_key] for item in data]
            values = [item[value_key] for item in data]

            colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c']
            wedges, texts, autotexts = ax.pie(
                values,
                labels=labels,
                autopct='%1.1f%%',
                colors=colors[:len(values)],
                textprops={'fontsize': 9, 'color': 'white'},
                startangle=90
            )

            # Make percentage text more readable
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(9)

            ax.set_title(title, color='white', fontsize=12, pad=10)

        self.figure.tight_layout(pad=1.0)
        self.canvas.draw()

class DataTableWidget(QWidget):
    """Widget for displaying tabular data."""

    def __init__(self, title: str = "Data Table", columns: List[str] = None):
        super().__init__()
        self.title = title
        self.columns = columns or []
        self.has_test_data = False
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the table widget UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title with test data indicator
        title_layout = QHBoxLayout()

        self.title_label = QLabel(self.title)
        self.title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        self.title_label.setStyleSheet("color: #FFFFFF; margin-bottom: 10px;")
        title_layout.addWidget(self.title_label)

        # Test data indicator (initially hidden)
        self.test_indicator = QLabel("🧪 TEST DATA")
        self.test_indicator.setFont(QFont("Segoe UI", 8, QFont.Weight.Bold))
        self.test_indicator.setStyleSheet("""
            color: #f39c12;
            background-color: #2C2C2C;
            border: 1px solid #f39c12;
            border-radius: 3px;
            padding: 2px 6px;
            margin-left: 10px;
        """)
        self.test_indicator.hide()
        title_layout.addWidget(self.test_indicator)

        title_layout.addStretch()
        layout.addLayout(title_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setStyleSheet("""
            QTableWidget {
                background-color: #2C2C2C;
                color: #FFFFFF;
                border: 1px solid #404040;
                gridline-color: #404040;
                font-size: 12px;
                selection-background-color: #3498db;
            }
            QTableWidget::item {
                padding: 8px 10px;
                border-bottom: 1px solid #404040;
                min-height: 20px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #404040;
            }
            QHeaderView::section {
                background-color: #404040;
                color: #FFFFFF;
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 11px;
                min-height: 25px;
            }
            QHeaderView::section:hover {
                background-color: #505050;
            }
        """)
        
        # Set columns
        if self.columns:
            self.table.setColumnCount(len(self.columns))
            self.table.setHorizontalHeaderLabels(self.columns)
        
        # Configure table
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Interactive)
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # Set minimum column widths for better readability
        if self.columns:
            for i, column in enumerate(self.columns):
                if column.lower() in ['name', 'entity_name', 'description']:
                    self.table.setColumnWidth(i, 150)
                elif column.lower() in ['code', 'action', 'status']:
                    self.table.setColumnWidth(i, 80)
                elif column.lower() in ['amount', 'revenue', 'total_revenue', 'total_amount']:
                    self.table.setColumnWidth(i, 100)
                else:
                    self.table.setColumnWidth(i, 90)
        
        layout.addWidget(self.table)
    
    def update_data(self, data: List[Dict[str, Any]]):
        """Update table data."""
        if not data:
            self.table.setRowCount(0)
            self.test_indicator.hide()
            return

        # Check if any data is test data
        has_test_data = any(row.get('is_test_data', False) for row in data)
        if has_test_data:
            self.test_indicator.show()
        else:
            self.test_indicator.hide()

        self.table.setRowCount(len(data))

        for row_idx, row_data in enumerate(data):
            for col_idx, column in enumerate(self.columns):
                value = row_data.get(column, "")
                if isinstance(value, (int, float)):
                    if column.lower() in ['revenue', 'amount', 'total_revenue', 'total_amount']:
                        value = f"${value:,.2f}"
                    else:
                        value = str(value)
                elif value is None:
                    value = ""
                else:
                    value = str(value)

                item = QTableWidgetItem(value)
                item.setFlags(item.flags() & ~Qt.ItemFlag.ItemIsEditable)

                # Highlight test data rows
                if row_data.get('is_test_data', False):
                    item.setStyleSheet("background-color: #3A2A1A; color: #f39c12;")

                self.table.setItem(row_idx, col_idx, item)

class SystemHealthWidget(QWidget):
    """Widget for displaying system health metrics."""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the system health widget UI."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title_label = QLabel("System Health")
        title_label.setFont(QFont("Segoe UI", 14, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #FFFFFF; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Health metrics
        self.cpu_progress = self.create_progress_bar("CPU Usage")
        self.memory_progress = self.create_progress_bar("Memory Usage")
        self.disk_progress = self.create_progress_bar("Disk Usage")
        
        layout.addWidget(self.cpu_progress['widget'])
        layout.addWidget(self.memory_progress['widget'])
        layout.addWidget(self.disk_progress['widget'])
        
        # Database status
        self.db_status_label = QLabel("Database: Checking...")
        self.db_status_label.setStyleSheet("color: #FFFFFF; margin-top: 10px;")
        layout.addWidget(self.db_status_label)
        
        # Record counts
        self.records_label = QLabel("Records: Loading...")
        self.records_label.setStyleSheet("color: #CCCCCC; font-size: 11px;")
        layout.addWidget(self.records_label)
    
    def create_progress_bar(self, label: str) -> Dict:
        """Create a labeled progress bar."""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 3, 0, 3)
        layout.setSpacing(3)

        label_widget = QLabel(label)
        label_widget.setStyleSheet("color: #FFFFFF; font-size: 10px; font-weight: bold;")
        label_widget.setWordWrap(True)
        layout.addWidget(label_widget)

        progress = QProgressBar()
        progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #404040;
                border-radius: 4px;
                background-color: #1E1E1E;
                text-align: center;
                color: #FFFFFF;
                font-size: 10px;
                font-weight: bold;
                min-height: 18px;
                max-height: 18px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        progress.setMaximum(100)
        progress.setMinimumHeight(18)
        progress.setMaximumHeight(18)
        layout.addWidget(progress)

        return {'widget': widget, 'label': label_widget, 'progress': progress}
    
    def update_health_data(self, health_data: Dict[str, Any]):
        """Update system health display."""
        # Update progress bars
        self.cpu_progress['progress'].setValue(int(health_data.get('cpu_percent', 0)))
        self.memory_progress['progress'].setValue(int(health_data.get('memory_percent', 0)))
        self.disk_progress['progress'].setValue(int(health_data.get('disk_percent', 0)))
        
        # Update labels with detailed info
        memory_used = health_data.get('memory_used_gb', 0)
        memory_total = health_data.get('memory_total_gb', 0)
        self.memory_progress['label'].setText(f"Memory Usage ({memory_used:.1f}GB / {memory_total:.1f}GB)")
        
        disk_used = health_data.get('disk_used_gb', 0)
        disk_total = health_data.get('disk_total_gb', 0)
        self.disk_progress['label'].setText(f"Disk Usage ({disk_used:.1f}GB / {disk_total:.1f}GB)")
        
        # Database status
        db_status = health_data.get('database_status', 'Unknown')
        status_color = "#2ecc71" if db_status == "Connected" else "#e74c3c"
        self.db_status_label.setText(f"Database: {db_status}")
        self.db_status_label.setStyleSheet(f"color: {status_color}; margin-top: 10px;")
        
        # Record counts
        counts = health_data.get('record_counts', {})
        records_text = f"Records: {counts.get('customers', 0)} customers, {counts.get('suppliers', 0)} suppliers, {counts.get('invoices', 0)} invoices"
        self.records_label.setText(records_text)
