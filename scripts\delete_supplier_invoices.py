"""
<PERSON>ript to delete all supplier invoices and their lines from the database for Green Nest Trade Co.
This script will delete all InvoiceLine and Invoice records where the invoice is a supplier invoice (supplier_id is not None), and reset Supplier.last_invoice_number to 0 for all suppliers.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.supplier import Supplier  # Ensure Supplier is imported before Invoice
from database.session import SessionLocal
from models.invoice import Invoice, InvoiceLine


def delete_supplier_invoices_and_reset_counters():
    session = SessionLocal()
    try:
        # Find all supplier invoice IDs
        supplier_invoice_ids = [inv.id for inv in session.query(Invoice.id).filter(Invoice.supplier_id != None)]
        if supplier_invoice_ids:
            # Delete all invoice lines for these invoices
            deleted_lines = session.query(InvoiceLine).filter(InvoiceLine.invoice_id.in_(supplier_invoice_ids)).delete(synchronize_session=False)
            # Delete the supplier invoices themselves
            deleted_invoices = session.query(Invoice).filter(Invoice.id.in_(supplier_invoice_ids)).delete(synchronize_session=False)
        else:
            deleted_lines = 0
            deleted_invoices = 0
        # Reset last_invoice_number for all suppliers
        updated_suppliers = session.query(Supplier).update({Supplier.last_invoice_number: 0})
        session.commit()
        print(f"Deleted {deleted_lines} invoice lines and {deleted_invoices} supplier invoices from the database.")
        print(f"Reset last_invoice_number to 0 for {updated_suppliers} suppliers.")
    except Exception as e:
        session.rollback()
        print(f"Error while deleting supplier invoices or resetting counters: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    delete_supplier_invoices_and_reset_counters() 