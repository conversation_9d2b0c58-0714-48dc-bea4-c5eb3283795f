import sqlite3

def get_next_code(used_numbers):
    """Find the smallest available 3-digit number as string."""
    for i in range(1, 1000):
        code = f"CNEST-{i:03d}"
        if code not in used_numbers:
            return code
    raise Exception("No available customer codes!")

def main():
    db_path = 'data/green_nest_trade_co.db'
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    # Get all customers
    c.execute('SELECT id, c_code FROM customers ORDER BY id')
    customers = c.fetchall()
    # Collect used codes
    used_codes = set(row[1] for row in customers if row[1])
    updates = []
    for row in customers:
        cust_id, c_code = row
        if not c_code:
            new_code = get_next_code(used_codes)
            used_codes.add(new_code)
            updates.append((new_code, cust_id))
    # Update customers
    for code, cust_id in updates:
        c.execute('UPDATE customers SET c_code=? WHERE id=?', (code, cust_id))
    conn.commit()
    print(f"Assigned codes to {len(updates)} customers.")
    conn.close()

if __name__ == '__main__':
    main()
