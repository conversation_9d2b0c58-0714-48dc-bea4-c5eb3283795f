# Green Nest Trade Co Accounting Application

A modern, comprehensive accounting and business management solution for small and medium businesses. Green Nest Trade Co replaces legacy systems (like Microsoft Access) with a robust, user-friendly desktop application. It features secure user management, customer and product tracking, invoicing, financial reporting, and a modern UI built with PyQt6. The backend uses SQLite (with SQLAlchemy ORM) and is easily portable to other databases.

## Features

- **User Management**: Secure authentication with role-based access control
- **Customer Management**: Track customer information, contacts, and interactions
- **Product Catalog**: Manage inventory with categories and stock levels
- **Invoicing**: Create, edit, and track customer invoices
- **Financial Tracking**: Monitor payments, expenses, and financial reports
- **Modern UI**: Intuitive PyQt6-based desktop interface
- **Database**: SQLite database with SQLAlchemy ORM (compatible with other databases)

## Technology Stack

- **Language**: Python 3.8+
- **UI Framework**: PyQt6
- **ORM/Database**: SQLAlchemy, SQLite (default), pyodbc (for SQL Server)
- **Other Libraries**: python-dotenv, pandas, reportlab

## Quick Start

### Prerequisites
- Python 3.8 or higher
- pip (Python package manager)
- Git (optional, for version control)

### Installation

1. **Clone the repository** (or download and extract the ZIP file)
   ```bash
   git clone <repository-url>
   cd "Green Nest Trade Co"
   ```

2. **Set up a virtual environment** (recommended)
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure the application**
   - Copy `.env.example` to `.env`
   - Edit `.env` to configure database and other settings

5. **Initialize the database**
   ```bash
   python scripts/init_db.py
   ```

6. **Run the application**
   ```bash
   python run.py
   # or
   python main.py
   ```

## Default Login Credentials

- **Username**: admin
- **Password**: admin

## Main Project Files & Structure

```
Green Nest Trade Co/
├── config/                  # Configuration settings
│   └── settings.py
├── database/                # Database models and session
│   ├── database.py
│   ├── initial_data.py
│   └── access_db.py
├── models/                  # ORM models (User, Customer, Product, etc.)
├── modules/                 # Application modules (e.g., customer_management.py)
├── scripts/                 # Utility scripts
│   └── init_db.py
├── ui/                      # User interface components
│   ├── main_window.py
│   ├── login_window.py
│   ├── dark_theme.py
│   ├── invoice_management_widget.py
│   └── resources/           # Icons and images
├── utils/                   # Utility functions
│   ├── database.py
│   ├── datetime_utils.py
│   └── exceptions.py
├── add_sample_data.py       # (Optional) Script to add sample data
├── analyze_access.py        # (Optional) Data migration/analysis
├── run.py                   # Main entry point
├── main.py                  # Alternate entry point
├── requirements.txt         # Python dependencies
├── alembic.ini              # Database migration config
└── README.md                # Project documentation
```

*Note: Excludes test, sample, and venv files for clarity.*

## Development

- Follows [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide.
- To run the test suite (if present):
  ```bash
  python -m pytest
  ```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [Python](https://www.python.org/) and [PyQt6](https://www.riverbankcomputing.com/software/pyqt/)
- Database powered by [SQLAlchemy](https://www.sqlalchemy.org/)
- Icons by [Material Design Icons](https://material.io/resources/icons/)
