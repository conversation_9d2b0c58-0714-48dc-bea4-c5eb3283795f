{"tables": {"Copy of ztblInvoice": {"columns": [{"name": "InvNo", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "LineNo", "type": "COUNTER", "size": 10, "nullable": false, "primary_key": false}, {"name": "ProdId", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Quantity", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Rate", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Discount", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Detail", "type": "VARCHAR", "size": 50, "nullable": true, "primary_key": false}], "sample_data": [], "row_count": 0}, "ztblInvoice": {"columns": [{"name": "InvNo", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "LineNo", "type": "COUNTER", "size": 10, "nullable": false, "primary_key": false}, {"name": "ProdId", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Quantity", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Rate", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Discount", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Detail", "type": "VARCHAR", "size": 50, "nullable": true, "primary_key": false}], "sample_data": [], "row_count": 0}, "ztblInvoiceHead": {"columns": [{"name": "InvoiceNo", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Customer", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "date", "type": "DATETIME", "size": 19, "nullable": true, "primary_key": false}, {"name": "CommissionRate", "type": "REAL", "size": 24, "nullable": true, "primary_key": false}, {"name": "InvTotalKD", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "Commission", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "OtherExpenses", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "AirportDuty", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "CustomDuty", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "TransportCharges", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "InvTotalPayable", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}, {"name": "OtherExpDetails", "type": "VARCHAR", "size": 50, "nullable": true, "primary_key": false}, {"name": "QuantityReceived", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "QuantitySold", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "TBLQtyBalance", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Accounted", "type": "BIT", "size": 1, "nullable": false, "primary_key": false}, {"name": "UserNick", "type": "VARCHAR", "size": 50, "nullable": true, "primary_key": false}, {"name": "InvTotalKDAccounts", "type": "DOUBLE", "size": 53, "nullable": true, "primary_key": false}], "sample_data": [], "row_count": 0}, "zzzTblInvSub": {"columns": [{"name": "InvoiceNo", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "date", "type": "DATETIME", "size": 19, "nullable": true, "primary_key": false}, {"name": "InvNo", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "LineNo", "type": "COUNTER", "size": 10, "nullable": false, "primary_key": false}, {"name": "ProdId", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Quantity", "type": "INTEGER", "size": 10, "nullable": true, "primary_key": false}, {"name": "Rate", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Discount", "type": "CURRENCY", "size": 19, "nullable": true, "primary_key": false}, {"name": "Detail", "type": "VARCHAR", "size": 50, "nullable": true, "primary_key": false}], "sample_data": [{"InvoiceNo": 39877, "date": "2019-01-02 00:00:00", "InvNo": 39877, "LineNo": 31916, "ProdId": 8, "Quantity": 2, "Rate": "42.0000", "Discount": "0.0000", "Detail": null}, {"InvoiceNo": 39877, "date": "2019-01-02 00:00:00", "InvNo": 39877, "LineNo": 31920, "ProdId": 6, "Quantity": 4, "Rate": "15.0000", "Discount": "0.0000", "Detail": null}, {"InvoiceNo": 39877, "date": "2019-01-02 00:00:00", "InvNo": 39877, "LineNo": 31921, "ProdId": 17, "Quantity": 30, "Rate": "14.0000", "Discount": "0.0000", "Detail": null}], "row_count": 3}}, "relationships": []}