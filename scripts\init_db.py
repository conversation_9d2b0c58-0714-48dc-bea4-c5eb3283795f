#!/usr/bin/env python3
"""
Initialize the database with required tables and initial data.
"""
import sys
from pathlib import Path
import os
import json
from sqlalchemy.orm import sessionmaker
from database.session import engine
from models.customer import Customer
from models.supplier import Supplier

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent))

def main():
    """Initialize the database and create initial data."""
    from database.session import engine, Base, init_db
    from sqlalchemy_utils import database_exists, create_database
    import logging
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Get database URL from environment or use default
    db_url = str(engine.url)
    
    try:
        # Create database if it doesn't exist (for SQLite, this happens automatically)
        if not database_exists(db_url):
            logger.info(f"Creating database: {db_url}")
            create_database(db_url)
        
        # Create all tables
        logger.info("Creating database tables...")
        init_db()
        
        # Create initial data
        from database.initial_data import create_initial_data
        logger.info("Creating initial data...")
        create_initial_data()
        
        logger.info("Database initialization completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}", exc_info=True)
        return False

def export_customers_suppliers():
    session = sessionmaker(bind=engine)()
    customers = session.query(Customer).all()
    suppliers = session.query(Supplier).all()
    customers_data = [
        {c.name: getattr(c, name) for name in c.__table__.columns.keys()} for c in customers
    ]
    suppliers_data = [
        {s.name: getattr(s, name) for name in s.__table__.columns.keys()} for s in suppliers
    ]
    with open('customers_backup.json', 'w', encoding='utf-8') as f:
        json.dump(customers_data, f, ensure_ascii=False, indent=2)
    with open('suppliers_backup.json', 'w', encoding='utf-8') as f:
        json.dump(suppliers_data, f, ensure_ascii=False, indent=2)
    print('Exported customers and suppliers.')

def import_customers_suppliers():
    session = sessionmaker(bind=engine)()
    if os.path.exists('customers_backup.json'):
        with open('customers_backup.json', 'r', encoding='utf-8') as f:
            customers_data = json.load(f)
        for data in customers_data:
            c = Customer(**data)
            session.add(c)
    if os.path.exists('suppliers_backup.json'):
        with open('suppliers_backup.json', 'r', encoding='utf-8') as f:
            suppliers_data = json.load(f)
        for data in suppliers_data:
            s = Supplier(**data)
            session.add(s)
    session.commit()
    print('Imported customers and suppliers.')

if __name__ == "__main__":
    if main():
        sys.exit(0)
    else:
        sys.exit(1)
    if len(sys.argv) > 1 and sys.argv[1] == 'export':
        export_customers_suppliers()
    elif len(sys.argv) > 1 and sys.argv[1] == 'import':
        import_customers_suppliers()
    else:
        print('Usage: python scripts/init_db.py export|import')
