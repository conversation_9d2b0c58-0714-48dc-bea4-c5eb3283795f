import sqlite3

def get_next_code(used_numbers):
    """Find the smallest available 3-digit number as string."""
    for i in range(1, 1000):
        code = f"SNEST-{i:03d}"
        if code not in used_numbers:
            return code
    raise Exception("No available supplier codes!")

def main():
    db_path = 'data/green_nest_trade_co.db'
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    # Get all suppliers
    c.execute('SELECT id, s_code FROM suppliers ORDER BY id')
    suppliers = c.fetchall()
    # Collect used codes
    used_codes = set(row[1] for row in suppliers if row[1])
    updates = []
    for row in suppliers:
        supp_id, s_code = row
        if not s_code:
            new_code = get_next_code(used_codes)
            used_codes.add(new_code)
            updates.append((new_code, supp_id))
    # Update suppliers
    for code, supp_id in updates:
        c.execute('UPDATE suppliers SET s_code=? WHERE id=?', (code, supp_id))
    conn.commit()
    print(f"Assigned codes to {len(updates)} suppliers.")
    conn.close()

if __name__ == '__main__':
    main()
