#!/usr/bin/env python3
"""
Health check script for Green Nest Trade Co Application
Tests all critical components to ensure the app is runnable.
"""
import sys
import os
import traceback
from pathlib import Path

def test_imports():
    """Test all critical imports."""
    print("Testing imports...")

    try:
        # Core dependencies
        import PyQt6
        print("✅ PyQt6 imported successfully")

        import sqlalchemy
        print(f"✅ SQLAlchemy {sqlalchemy.__version__} imported successfully")

        import python_dotenv
        print("✅ python-dotenv imported successfully")

        import passlib
        print("✅ passlib imported successfully")

        # Optional dependencies (with graceful handling)
        optional_imports = [
            ('pandas', 'Pandas'),
            ('reportlab', 'ReportLab'),
            ('pyodbc', 'pyodbc'),
            ('openpyxl', 'openpyxl'),
        ]

        for module_name, display_name in optional_imports:
            try:
                __import__(module_name)
                print(f"✅ {display_name} imported successfully")
            except ImportError as e:
                print(f"⚠️  {display_name} not available: {e}")

        # Application modules
        from config import settings
        print("✅ Config settings imported successfully")

        from database.session import engine, SessionLocal, init_db
        print("✅ Database session imported successfully")

        from models.base import Base
        print("✅ Base model imported successfully")

        from models.user import User
        from models.customer import Customer
        from models.product import Product, ProductCategory
        from models.invoice import Invoice, InvoiceLine
        print("✅ All models imported successfully")

        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        print("✅ UI components imported successfully")

        return True

    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Test database connectivity and initialization."""
    print("\nTesting database...")
    
    try:
        from database.session import engine, init_db
        from sqlalchemy import text
        
        # Test database connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("✅ Database connection successful")
        
        # Test database initialization
        if init_db():
            print("✅ Database initialization successful")
        else:
            print("❌ Database initialization failed")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Database error: {e}")
        traceback.print_exc()
        return False

def test_ui_creation():
    """Test UI component creation without showing windows."""
    print("\nTesting UI creation...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from ui.login_window import LoginWindow
        from ui.main_window import MainWindow
        
        # Create application instance
        app = QApplication.instance() or QApplication(sys.argv)
        print("✅ QApplication created successfully")
        
        # Test login window creation
        login_window = LoginWindow()
        print("✅ Login window created successfully")
        
        # Test main window creation
        main_window = MainWindow()
        print("✅ Main window created successfully")
        
        # Clean up
        login_window.close()
        main_window.close()
        
        return True
        
    except Exception as e:
        print(f"❌ UI creation error: {e}")
        traceback.print_exc()
        return False

def test_file_structure():
    """Test that all required files and directories exist."""
    print("\nTesting file structure...")
    
    required_files = [
        "main.py",
        "run.py",
        "requirements.txt",
        "config/settings.py",
        "database/session.py",
        "ui/login_window.py",
        "ui/main_window.py",
    ]
    
    required_dirs = [
        "config",
        "database", 
        "models",
        "ui",
        "utils",
        "data",
        "logs"
    ]
    
    all_good = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_good = False
    
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/ directory exists")
        else:
            print(f"❌ {dir_path}/ directory missing")
            all_good = False
    
    return all_good

def main():
    """Run all health checks."""
    print("🔍 Green Nest Trade Co Application Health Check")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Imports", test_imports),
        ("Database", test_database),
        ("UI Creation", test_ui_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 HEALTH CHECK SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED! Your application is ready to run.")
        print("\nTo start the application, run:")
        print("  python main.py")
        print("  or")
        print("  python run.py")
    else:
        print("⚠️  SOME TESTS FAILED! Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
