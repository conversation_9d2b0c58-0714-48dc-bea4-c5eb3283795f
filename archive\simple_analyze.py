import pyodbc
from pathlib import Path

def analyze_access_db(db_path, username='', password=''):
    """Simple script to analyze an Access database."""
    # Try different connection strings
    conn_strs = [
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb, *.accdb)}};DBQ={db_path};UID={username};PWD={password};',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path}',
        f'DRIVER={{Microsoft Access Driver (*.mdb)}};DBQ={db_path};UID={username};PWD={password};',
    ]
    
    conn = None
    for conn_str in conn_strs:
        try:
            conn = pyodbc.connect(conn_str)
            print(f"Successfully connected using: {conn_str[:80]}...")
            break
        except pyodbc.Error as e:
            print(f"Connection failed: {e}")
            continue
    
    if not conn:
        print("Failed to connect to the database.")
        return
    
    cursor = conn.cursor()
    
    try:
        # Get all tables
        print("\n=== Tables ===")
        tables = [table.table_name for table in cursor.tables(tableType='TABLE') 
                 if not table.table_name.startswith('MSys')]
        
        for table in sorted(tables):
            print(f"\nTable: {table}")
            
            # Get column info
            try:
                columns = cursor.columns(table=table)
                print("Columns:")
                for col in columns:
                    print(f"  - {col.column_name}: {col.type_name}({col.column_size or '?'})")
                
                # Get sample data
                try:
                    cursor.execute(f'SELECT TOP 3 * FROM [{table}]')
                    rows = cursor.fetchall()
                    if rows:
                        print("Sample data:")
                        for row in rows[:2]:  # Show first 2 rows
                            print(f"  {row}")
                        if len(rows) > 2:
                            print("  ...")
                except Exception as e:
                    print(f"  Could not fetch sample data: {e}")
                    
            except Exception as e:
                print(f"  Could not get column info: {e}")
    
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()
        print("\nDatabase connection closed.")

if __name__ == "__main__":
    db_path = r"c:\HamzaZareiProgram\RabieAl-KhaleejMulti.mdb"
    username = "majid"
    password = "majid"
    
    print(f"Analyzing database: {db_path}")
    analyze_access_db(db_path, username, password)
