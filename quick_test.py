#!/usr/bin/env python3
"""
Quick test to verify the application can start without errors.
"""
import sys
import os

def main():
    print("🔍 Quick Application Test")
    print("=" * 30)
    
    try:
        # Test basic imports
        print("Testing core imports...")
        import PyQt6
        print("✅ PyQt6 imported")
        
        import sqlalchemy
        print("✅ SQLAlchemy imported")
        
        # Test application imports
        print("Testing application imports...")
        from config import settings
        print("✅ Settings imported")
        
        from database.session import engine
        print("✅ Database session imported")
        
        # Test database connection
        print("Testing database connection...")
        with engine.connect() as conn:
            result = conn.execute(sqlalchemy.text("SELECT 1"))
            print("✅ Database connection successful")
        
        print("\n🎉 ALL TESTS PASSED!")
        print("Your application is ready to run.")
        print("\nTo start the application:")
        print("  python main.py")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
